<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\Attendance;
use Carbon\Carbon;

class RecalculateAttendanceUndertime extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'attendance:recalculate-undertime';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Recalculate undertime for all attendance records using updated shift policies';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('Recalculating undertime for all attendance records...');

        $attendances = Attendance::with(['shift.policy'])
            ->whereNotNull('check_out_time')
            ->get();

        $updated = 0;

        foreach ($attendances as $attendance) {
            if (!$attendance->shift || !$attendance->shift->policy) {
                continue;
            }

            $shift = $attendance->shift;
            $policy = $shift->policy;

            // Calculate total minutes worked
            $checkInTime = Carbon::parse($attendance->check_in_time);
            $checkOutTime = Carbon::parse($attendance->check_out_time);
            $totalMinutesWorked = $checkInTime->diffInMinutes($checkOutTime);

            // Calculate scheduled minutes
            $shiftStart = Carbon::parse($checkInTime->toDateString() . ' ' . $shift->start_time);
            $shiftEnd = Carbon::parse($checkInTime->toDateString() . ' ' . $shift->end_time);
            if ($shift->spans_two_days && $shiftEnd->lt($shiftStart)) {
                $shiftEnd->addDay();
            }
            $scheduledMinutes = $shiftStart->diffInMinutes($shiftEnd);

            // Recalculate overtime/undertime
            [$overtime, $undertime] = $this->calculateOvertimeUndertime(
                $totalMinutesWorked,
                $scheduledMinutes,
                $shift,
                $policy
            );

            // Update if values changed
            if ($attendance->overtime_minutes != $overtime || $attendance->undertime_minutes != $undertime) {
                $attendance->update([
                    'overtime_minutes' => $overtime,
                    'undertime_minutes' => $undertime,
                ]);
                $updated++;
            }
        }

        $this->info("Recalculated undertime for {$updated} attendance records.");
        return 0;
    }

    /**
     * Calculate overtime/undertime using the same logic as AttendanceController
     */
    private function calculateOvertimeUndertime($totalMinutesWorked, $scheduledMinutes, $shift, $policy)
    {
        // Calculate payable minutes (total worked minus unpaid break)
        $payableMinutes = $totalMinutesWorked - $shift->unpaid_break_minutes;

        // Calculate expected payable minutes (scheduled minus unpaid break)
        $expectedPayableMinutes = $scheduledMinutes - $shift->unpaid_break_minutes;

        // Calculate minimum required work time using grace periods
        $minimumRequiredMinutes = $expectedPayableMinutes - $policy->late_grace_period_minutes - $policy->early_leave_grace_period_minutes;

        // Ensure minimum doesn't go below zero
        $minimumRequiredMinutes = max(0, $minimumRequiredMinutes);

        $overtime = 0;
        $undertime = 0;

        // Check for undertime first
        if ($payableMinutes < $minimumRequiredMinutes) {
            $undertime = $minimumRequiredMinutes - $payableMinutes;
        }
        // Check for overtime (only if not undertime)
        elseif ($payableMinutes > $expectedPayableMinutes + $policy->overtime_grace_period_minutes) {
            $overtime = $payableMinutes - $expectedPayableMinutes - $policy->overtime_grace_period_minutes;
        }

        return [$overtime, $undertime];
    }
}
