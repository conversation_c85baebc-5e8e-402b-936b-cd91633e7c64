<?php

namespace Tests\Feature;

use Tests\TestCase;
use App\Models\User;
use App\Models\EmployeeProfile;
use App\Models\Shift;
use App\Models\ShiftPolicy;
use App\Models\Attendance;
use App\Http\Controllers\AttendanceController;
use Carbon\Carbon;
use Illuminate\Foundation\Testing\RefreshDatabase;

class UndertimeCalculationTest extends TestCase
{
    use RefreshDatabase;

    public function test_enhanced_undertime_calculation_with_grace_periods()
    {
        // Create a test shift with specific policies
        $shift = Shift::factory()->create([
            'name' => 'Test Shift',
            'start_time' => '09:00:00',
            'end_time' => '17:00:00',
            'unpaid_break_minutes' => 60,
            'spans_two_days' => false,
        ]);

        $policy = ShiftPolicy::create([
            'shift_id' => $shift->id,
            'late_grace_period_minutes' => 15,
            'early_leave_grace_period_minutes' => 10,
            'overtime_grace_period_minutes' => 15,
            'check_in_allowance_before_minutes' => 30,
        ]);

        // Create test user and employee profile
        $user = User::factory()->create();
        $employeeProfile = EmployeeProfile::factory()->create(['user_id' => $user->id]);

        // Test scenario: Employee works less than minimum required time
        // Shift: 9:00 AM - 5:00 PM (8 hours = 480 minutes)
        // Break: 60 minutes
        // Expected payable: 480 - 60 = 420 minutes
        // Grace periods: 15 (late) + 10 (early) = 25 minutes
        // Minimum required: 420 - 25 = 395 minutes
        
        // Employee works: 9:30 AM - 4:00 PM = 6.5 hours = 390 minutes
        // Payable: 390 - 60 = 330 minutes
        // Since 330 < 395, undertime = 395 - 330 = 65 minutes

        $checkInTime = Carbon::parse('2025-01-15 09:30:00');
        $checkOutTime = Carbon::parse('2025-01-15 16:00:00');
        
        $attendance = Attendance::create([
            'employee_profile_id' => $employeeProfile->id,
            'shift_id' => $shift->id,
            'check_in_time' => $checkInTime,
            'check_out_time' => $checkOutTime,
            'status' => 'late',
            'check_in_latitude' => 0,
            'check_in_longitude' => 0,
            'check_in_selfie_path' => 'test.jpg',
        ]);

        // Use reflection to access the private method
        $controller = new AttendanceController();
        $reflection = new \ReflectionClass($controller);
        $method = $reflection->getMethod('calculateOvertimeUndertime');
        $method->setAccessible(true);

        $totalMinutesWorked = $checkInTime->diffInMinutes($checkOutTime); // 390 minutes
        $scheduledMinutes = 8 * 60; // 480 minutes

        [$overtime, $undertime] = $method->invoke(
            $controller,
            $totalMinutesWorked,
            $scheduledMinutes,
            $shift,
            $policy
        );

        // Verify the calculation
        $this->assertEquals(0, $overtime, 'Should have no overtime');
        $this->assertEquals(65, $undertime, 'Should have 65 minutes undertime');

        // Test another scenario: Employee works exactly minimum required time
        $checkInTime2 = Carbon::parse('2025-01-16 09:15:00'); // 15 min late (within grace)
        $checkOutTime2 = Carbon::parse('2025-01-16 16:50:00'); // 10 min early (within grace)
        // Total: 7h 35m = 455 minutes, Payable: 455 - 60 = 395 minutes (exactly minimum)

        $totalMinutesWorked2 = $checkInTime2->diffInMinutes($checkOutTime2);
        [$overtime2, $undertime2] = $method->invoke(
            $controller,
            $totalMinutesWorked2,
            $scheduledMinutes,
            $shift,
            $policy
        );

        $this->assertEquals(0, $overtime2, 'Should have no overtime');
        $this->assertEquals(0, $undertime2, 'Should have no undertime when working exactly minimum required');
    }

    public function test_overtime_calculation_with_grace_period()
    {
        // Create a test shift
        $shift = Shift::factory()->create([
            'name' => 'Test Shift',
            'start_time' => '09:00:00',
            'end_time' => '17:00:00',
            'unpaid_break_minutes' => 60,
            'spans_two_days' => false,
        ]);

        $policy = ShiftPolicy::create([
            'shift_id' => $shift->id,
            'late_grace_period_minutes' => 15,
            'early_leave_grace_period_minutes' => 10,
            'overtime_grace_period_minutes' => 15,
            'check_in_allowance_before_minutes' => 30,
        ]);

        // Test overtime scenario: Employee works beyond grace period
        // Expected payable: 420 minutes
        // Overtime grace: 15 minutes
        // Employee works: 9:00 AM - 6:00 PM = 9 hours = 540 minutes
        // Payable: 540 - 60 = 480 minutes
        // Overtime: 480 - 420 - 15 = 45 minutes

        $controller = new AttendanceController();
        $reflection = new \ReflectionClass($controller);
        $method = $reflection->getMethod('calculateOvertimeUndertime');
        $method->setAccessible(true);

        $totalMinutesWorked = 9 * 60; // 540 minutes
        $scheduledMinutes = 8 * 60; // 480 minutes

        [$overtime, $undertime] = $method->invoke(
            $controller,
            $totalMinutesWorked,
            $scheduledMinutes,
            $shift,
            $policy
        );

        $this->assertEquals(45, $overtime, 'Should have 45 minutes overtime');
        $this->assertEquals(0, $undertime, 'Should have no undertime');
    }
}
