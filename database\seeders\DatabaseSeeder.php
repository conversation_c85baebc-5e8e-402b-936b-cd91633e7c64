<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\User;
use App\Models\EmployeeProfile;
use App\Models\Shift;
use App\Models\ShiftPolicy;
use App\Models\WorkZone;
use App\Models\EmployeeShift;
use App\Models\Attendance;
use Illuminate\Support\Facades\Hash;
use Carbon\Carbon;

class DatabaseSeeder extends Seeder
{
    public function run(): void
    {
        $this->call(RolesAndPermissionsSeeder::class);

        // Create Admin and necessary shifts/zones first
        $superAdminUser = User::factory()->create(['name' => 'Admin User', 'email' => '<EMAIL>', 'password' => Hash::make('password')]);
        EmployeeProfile::factory()->create(['user_id' => $superAdminUser->id, 'first_name' => 'Admin', 'last_name' => 'User', 'employee_id_number' => 'EMP0000']);
        $superAdminUser->assignRole('Super Admin');
        
        $dayShift = Shift::factory()->dayShift()->create(['unpaid_break_minutes' => 60]);
        $dayShift->policy()->create();
        $nightShift = Shift::factory()->nightShift()->create(['unpaid_break_minutes' => 60]);
        $nightShift->policy()->create(['late_grace_period_minutes' => 20]);
        $workZone1 = WorkZone::factory()->create(['name' => 'Main Office']);
        $workZone2 = WorkZone::factory()->create(['name' => 'Warehouse A']);

        // Create weekend shift for testing
        $weekendShift = Shift::factory()->create([
            'name' => 'Weekend Shift',
            'start_time' => '10:00:00',
            'end_time' => '18:00:00',
            'unpaid_break_minutes' => 30,
            'spans_two_days' => false,
        ]);
        $weekendShift->policy()->create([
            'late_grace_period_minutes' => 15,
            'early_leave_grace_period_minutes' => 10,
            'overtime_grace_period_minutes' => 15,
            'check_in_allowance_before_minutes' => 30,
        ]);

        // Create 15 Users, then loop to create profiles and data
        User::factory(15)->create()->each(function ($user) use ($dayShift, $nightShift, $weekendShift, $workZone1, $workZone2) {
            // Generate consistent names for user and profile
            $firstName = fake()->firstName;
            $lastName = fake()->lastName;
            $fullName = $firstName . ' ' . $lastName;

            // Update user name to match what we'll use in profile
            $user->update(['name' => $fullName]);

            $profile = EmployeeProfile::factory()->create([
                'user_id' => $user->id,
                'first_name' => $firstName,
                'last_name' => $lastName,
            ]);
            $user->assignRole('Employee');

            $isDayWorker = $user->id % 2 != 0;
            $primaryShift = $isDayWorker ? $dayShift : $nightShift;
            $primaryZone = $isDayWorker ? $workZone1 : $workZone2;

            // Regular weekday shift assignment
            EmployeeShift::create([
                'employee_profile_id' => $profile->id,
                'shift_id' => $primaryShift->id,
                'work_zone_id' => $primaryZone->id,
                'start_date' => Carbon::parse('2025-01-01'),
                'end_date' => null,
            ]);

            // Some employees also work weekends (every 3rd employee)
            if ($user->id % 3 == 0) {
                EmployeeShift::create([
                    'employee_profile_id' => $profile->id,
                    'shift_id' => $weekendShift->id,
                    'work_zone_id' => $primaryZone->id,
                    'start_date' => Carbon::parse('2025-01-01'),
                    'end_date' => null,
                ]);
            }

            // Create different employee personas for more realistic testing
            $employeeType = $user->id % 4; // 4 different types
            // 0: Model employee (punctual, rarely absent)
            // 1: Slacker (often late, leaves early)
            // 2: Workaholic (overtime, early arrival)
            // 3: Inconsistent (mixed behavior)
            $startDate = now()->subMonths(6)->startOfMonth();
            $endDate = now()->subDay();

            // Check if this employee works weekends
            $worksWeekends = $user->id % 3 == 0;

            for ($date = $startDate->copy(); $date->lt($endDate); $date->addDay()) {
                // Skip weekends unless employee works weekends
                if ($date->isWeekend() && !$worksWeekends) continue;

                // Use weekend shift for weekend days
                $currentShift = ($date->isWeekend() && $worksWeekends) ? $weekendShift : $primaryShift;

                // --- ENHANCED REALISTIC VARIANCE LOGIC ---
                $roll = rand(1, 100); // Roll a 100-sided die for the day's event

                // Adjust absence rate based on employee type
                $absenceThreshold = match($employeeType) {
                    0 => 2,  // Model employee: 2% absence
                    1 => 8,  // Slacker: 8% absence
                    2 => 1,  // Workaholic: 1% absence
                    3 => 5,  // Inconsistent: 5% absence
                };

                if ($roll <= $absenceThreshold) {
                    continue; // Skip creating attendance record (absent day)
                }

                // Adjust incomplete attendance rate based on employee type
                $incompleteThreshold = $absenceThreshold + match($employeeType) {
                    0 => 1,  // Model employee: 1% incomplete
                    1 => 5,  // Slacker: 5% incomplete
                    2 => 2,  // Workaholic: 2% incomplete
                    3 => 4,  // Inconsistent: 4% incomplete
                };

                $incompleteAttendance = ($roll > $absenceThreshold && $roll <= $incompleteThreshold);

                // 2% chance of half-day (early leave for appointment, etc.)
                $halfDay = ($roll > $incompleteThreshold && $roll <= $incompleteThreshold + 2);

                // Default to a normal day
                $checkInVariance = rand(-10, 10);
                $checkOutVariance = rand(-10, 20);

                // Apply employee type behavior patterns
                switch ($employeeType) {
                    case 0: // Model employee
                        if ($roll <= 85) { // 85% normal days
                            $checkInVariance = rand(-5, 5);
                            $checkOutVariance = rand(-10, 30);
                        } else { // 15% slightly late/early days
                            $checkInVariance = rand(5, 15);
                            $checkOutVariance = rand(-20, 10);
                        }
                        break;

                    case 1: // Slacker
                        if ($roll <= 60) { // 60% problematic days
                            $checkInVariance = rand(15, 60);
                            $checkOutVariance = rand(-90, 0);
                        } else if ($roll <= 80) { // 20% normal days
                            // Use default values
                        } else { // 20% surprisingly good days
                            $checkInVariance = rand(-10, 0);
                            $checkOutVariance = rand(30, 90);
                        }
                        break;

                    case 2: // Workaholic
                        if ($roll <= 75) { // 75% overtime days
                            $checkInVariance = rand(-20, 0);
                            $checkOutVariance = rand(60, 180);
                        } else if ($roll <= 90) { // 15% normal days
                            // Use default values
                        } else { // 10% rare early days
                            $checkInVariance = rand(0, 10);
                            $checkOutVariance = rand(-30, 0);
                        }
                        break;

                    case 3: // Inconsistent
                        if ($roll <= 30) { // 30% late days
                            $checkInVariance = rand(20, 45);
                            $checkOutVariance = rand(-60, 30);
                        } else if ($roll <= 60) { // 30% normal days
                            // Use default values
                        } else { // 40% early/overtime days
                            $checkInVariance = rand(-15, 5);
                            $checkOutVariance = rand(30, 120);
                        }
                        break;
                }

                // Handle special cases
                if ($halfDay) {
                    // Half day - leave around lunch time
                    $checkOutVariance = rand(-240, -180); // Leave 3-4 hours early
                }

                $this->createAttendanceRecord($profile, $currentShift, $date, $checkInVariance, $checkOutVariance, $incompleteAttendance);
            }

            // Add some recent incomplete attendances for testing (last 7 days)
            // Only add if no existing records for recent dates to prevent duplicates
            $this->addRecentTestData($profile, $primaryShift, $weekendShift, $worksWeekends);
        });
    }

    private function addRecentTestData($profile, $primaryShift, $weekendShift, $worksWeekends)
    {
        // Add some recent data for the last 14 days to test dashboard features (including more weekends)
        for ($i = 13; $i >= 0; $i--) {
            $date = now()->subDays($i);

            // Skip weekends unless employee works weekends
            if ($date->isWeekend() && !$worksWeekends) continue;

            // Check if attendance already exists for this date to prevent duplicates
            $existingAttendance = Attendance::where('employee_profile_id', $profile->id)
                ->whereDate('check_in_time', $date->toDateString())
                ->exists();

            if ($existingAttendance) {
                continue; // Skip if attendance already exists for this date
            }

            $currentShift = ($date->isWeekend() && $worksWeekends) ? $weekendShift : $primaryShift;

            // For weekend workers, ensure they have some weekend attendance
            if ($worksWeekends && $date->isWeekend()) {
                // 80% chance of weekend attendance for weekend workers
                $roll = rand(1, 100);
                if ($roll <= 80) {
                    if ($roll <= 10) { // 10% chance of incomplete weekend attendance
                        $this->createAttendanceRecord($profile, $currentShift, $date, rand(-5, 15), 0, true);
                    } else { // Normal weekend attendance
                        $checkInVariance = rand(-10, 20);
                        $checkOutVariance = rand(-30, 60);
                        $this->createAttendanceRecord($profile, $currentShift, $date, $checkInVariance, $checkOutVariance, false);
                    }
                }
                continue;
            }

            // Regular weekday logic
            $roll = rand(1, 100);

            if ($roll <= 15) { // 15% chance of incomplete attendance
                $this->createAttendanceRecord($profile, $currentShift, $date, rand(-5, 15), 0, true);
            } elseif ($roll <= 25) { // 10% chance of being absent
                continue;
            } else { // Normal attendance
                $checkInVariance = rand(-10, 20);
                $checkOutVariance = rand(-30, 60);
                $this->createAttendanceRecord($profile, $currentShift, $date, $checkInVariance, $checkOutVariance, false);
            }
        }
    }

    private function createAttendanceRecord($employee, $shift, $date, $checkInVariance, $checkOutVariance, $incompleteAttendance = false)
    {
        $policy = $shift->policy;
        $shiftStartTime = Carbon::parse($date->toDateString() . ' ' . $shift->start_time);
        $checkInTime = $shiftStartTime->copy()->addMinutes($checkInVariance);
        $shiftEndTime = Carbon::parse($date->toDateString() . ' ' . $shift->end_time);
        if ($shift->spans_two_days) { $shiftEndTime->addDay(); }

        $isLate = $checkInTime->gt($shiftStartTime->copy()->addMinutes($policy->late_grace_period_minutes));

        // Handle incomplete attendance (no check-out)
        if ($incompleteAttendance) {
            Attendance::create([
                'employee_profile_id' => $employee->id,
                'shift_id' => $shift->id,
                'check_in_time' => $checkInTime,
                'check_out_time' => null, // No check-out time
                'status' => $isLate ? 'late' : 'on_time',
                'overtime_minutes' => 0,
                'undertime_minutes' => 0,
                'check_in_latitude' => 0,
                'check_in_longitude' => 0,
                'check_in_selfie_path' => 'dummy.jpg',
                'check_in_notes' => 'Forgot to check out',
            ]);
            return;
        }

        // Normal attendance with check-out
        $checkOutTime = $shiftEndTime->copy()->addMinutes($checkOutVariance);
        $totalMinutesOnClock = $checkInTime->diffInMinutes($checkOutTime);
        $scheduledMinutes = $shiftStartTime->diffInMinutes($shiftEndTime);
        $payableMinutes = $totalMinutesOnClock - $shift->unpaid_break_minutes;
        $netDifference = $payableMinutes - ($scheduledMinutes - $shift->unpaid_break_minutes);
        $overtime = 0;
        $undertime = 0;

        // Use enhanced calculation logic
        [$overtime, $undertime] = $this->calculateOvertimeUndertime(
            $totalMinutesOnClock,
            $scheduledMinutes,
            $shift,
            $policy
        );

        // Add realistic notes for various scenarios
        $notes = $this->generateRealisticNotes($checkInVariance, $checkOutVariance, $overtime, $undertime);

        Attendance::create([
            'employee_profile_id' => $employee->id,
            'shift_id' => $shift->id,
            'check_in_time' => $checkInTime,
            'check_out_time' => $checkOutTime,
            'status' => $isLate ? 'late' : 'on_time',
            'overtime_minutes' => $overtime,
            'undertime_minutes' => $undertime,
            'check_in_latitude' => 0,
            'check_in_longitude' => 0,
            'check_in_selfie_path' => 'dummy.jpg',
            'check_in_notes' => $notes['check_in'],
            'check_out_notes' => $notes['check_out'],
        ]);
    }

    private function generateRealisticNotes($checkInVariance, $checkOutVariance, $overtime, $undertime)
    {
        $checkInNotes = null;
        $checkOutNotes = null;

        // Check-in notes
        if ($checkInVariance > 30) {
            $reasons = ['Traffic jam', 'Car trouble', 'Doctor appointment', 'Family emergency', 'Overslept'];
            $checkInNotes = $reasons[array_rand($reasons)];
        } elseif ($checkInVariance < -15) {
            $reasons = ['Early for meeting', 'Avoiding traffic', 'Finished tasks from yesterday'];
            $checkInNotes = $reasons[array_rand($reasons)];
        }

        // Check-out notes
        if ($overtime > 60) {
            $reasons = ['Project deadline', 'Covering for colleague', 'System maintenance', 'Client meeting ran late'];
            $checkOutNotes = $reasons[array_rand($reasons)];
        } elseif ($undertime > 60) {
            $reasons = ['Medical appointment', 'Family emergency', 'Feeling unwell', 'Pre-approved early leave'];
            $checkOutNotes = $reasons[array_rand($reasons)];
        }

        return ['check_in' => $checkInNotes, 'check_out' => $checkOutNotes];
    }

    /**
     * Enhanced overtime/undertime calculation using shift policies.
     * Same logic as in AttendanceController for consistency.
     */
    private function calculateOvertimeUndertime($totalMinutesWorked, $scheduledMinutes, $shift, $policy)
    {
        // Calculate payable minutes (total worked minus unpaid break)
        $payableMinutes = $totalMinutesWorked - $shift->unpaid_break_minutes;

        // Calculate expected payable minutes (scheduled minus unpaid break)
        $expectedPayableMinutes = $scheduledMinutes - $shift->unpaid_break_minutes;

        // Calculate minimum required work time using grace periods
        $minimumRequiredMinutes = $expectedPayableMinutes - $policy->late_grace_period_minutes - $policy->early_leave_grace_period_minutes;

        // Ensure minimum doesn't go below zero
        $minimumRequiredMinutes = max(0, $minimumRequiredMinutes);

        $overtime = 0;
        $undertime = 0;

        // Check for undertime first
        if ($payableMinutes < $minimumRequiredMinutes) {
            $undertime = $minimumRequiredMinutes - $payableMinutes;
        }
        // Check for overtime (only if not undertime)
        elseif ($payableMinutes > $expectedPayableMinutes + $policy->overtime_grace_period_minutes) {
            $overtime = $payableMinutes - $expectedPayableMinutes - $policy->overtime_grace_period_minutes;
        }

        return [$overtime, $undertime];
    }
}