<script setup>
import { ref, watchEffect, computed } from 'vue';
import { Bar } from 'vue-chartjs';
import { Chart as ChartJS, Title, Tooltip, Legend, BarElement, CategoryScale, LinearScale } from 'chart.js';
import annotationPlugin from 'chartjs-plugin-annotation';
import { useResizeObserver, useDebounceFn } from '@vueuse/core';

ChartJS.register(Title, Tooltip, Legend, BarElement, CategoryScale, LinearScale, annotationPlugin);
const props = defineProps({
    activityData: Object,
    incompleteAttendances: Object,
    undertimeData: Object,
    shiftData: Object
});
const chartData = ref({});
const chartRef = ref(null);
const containerRef = ref(null);

watchEffect(() => {
    // Only show days that are present in the activity data (working days)
    const labels = Object.keys(props.activityData || {});

    // Prepare data for the enhanced visualization
    const expectedHoursData = [];
    const completedHoursData = [];
    const overtimeHoursData = [];
    const missingHoursData = [];

    labels.forEach(day => {
        const actualHours = props.activityData?.[day] || 0;
        const expectedHours = props.shiftData?.[day]?.expected_hours || 0;
        const hasIncomplete = props.incompleteAttendances?.[day] === true;
        const hasShift = props.shiftData?.[day] !== undefined;

        // If no shift is scheduled for this day, just show actual hours worked (if any)
        if (!hasShift || expectedHours === 0) {
            // Only show actual hours if they worked (no missing hours for non-shift days)
            completedHoursData.push(actualHours);
            missingHoursData.push(0);
            overtimeHoursData.push(0);
        } else {
            // Calculate different segments for scheduled days
            const completedHours = Math.min(actualHours, expectedHours);
            const overtimeHours = Math.max(0, actualHours - expectedHours);
            const missingHours = Math.max(0, expectedHours - actualHours);

            // For incomplete attendance, show what they've worked so far
            if (hasIncomplete) {
                completedHoursData.push(actualHours);
                missingHoursData.push(Math.max(0, expectedHours - actualHours));
                overtimeHoursData.push(0);
            } else {
                completedHoursData.push(completedHours);
                missingHoursData.push(missingHours);
                overtimeHoursData.push(overtimeHours);
            }
        }

        expectedHoursData.push(expectedHours);
    });

    chartData.value = {
        labels,
        datasets: [
            {
                label: 'Completed Hours',
                backgroundColor: labels.map(day => {
                    const hasIncomplete = props.incompleteAttendances?.[day] === true;
                    const hasUndertime = props.undertimeData?.[day] === true;
                    const actualHours = props.activityData?.[day] || 0;
                    const expectedHours = props.shiftData?.[day]?.expected_hours || 0;
                    const hasOvertime = actualHours > expectedHours && expectedHours > 0;

                    if (hasIncomplete) {
                        return '#f59e0b'; // Orange for incomplete
                    } else if (hasUndertime) {
                        return '#ef4444'; // Red for undertime
                    } else if (hasOvertime) {
                        return '#22c55e'; // Green for overtime
                    } else {
                        return '#3b82f6'; // Blue for normal/complete
                    }
                }),
                borderColor: '#1e40af',
                borderWidth: 1,
                borderRadius: 4,
                data: completedHoursData,
                barThickness: 25,
                maxBarThickness: 35,
                stack: 'hours'
            },
            {
                label: 'Missing Hours',
                backgroundColor: 'rgba(229, 231, 235, 0.8)', // Light gray for missing hours
                borderColor: '#9ca3af',
                borderWidth: 1,
                borderRadius: 4,
                data: missingHoursData,
                barThickness: 25,
                maxBarThickness: 35,
                stack: 'hours'
            },
            {
                label: 'Overtime Hours',
                backgroundColor: '#16a34a', // Darker green for overtime (stacked on top)
                borderColor: '#15803d',
                borderWidth: 1,
                borderRadius: 4,
                data: overtimeHoursData,
                barThickness: 25,
                maxBarThickness: 35,
                stack: 'hours'
            }
        ],
    };
});

// ### THE FIX: Debounced Resize Handler ###
// This creates a new function that will only run after 100ms of no activity.
const debouncedResize = useDebounceFn(() => {
    if (chartRef.value?.chart) {
        chartRef.value.chart.resize();
    }
}, 100);

useResizeObserver(containerRef, debouncedResize);

const chartOptions = {
    responsive: true,
    maintainAspectRatio: false,
    interaction: {
        mode: 'index',
        intersect: false,
    },
    plugins: {
        legend: { display: false },
        annotation: {
            annotations: computed(() => {
                const annotations = {};
                const labels = Object.keys(props.activityData || {});

                labels.forEach((day, index) => {
                    const hasIncomplete = props.incompleteAttendances?.[day] === true;
                    const actualHours = props.activityData?.[day] || 0;
                    const expectedHours = props.shiftData?.[day]?.expected_hours || 0;

                    if (hasIncomplete && expectedHours > actualHours) {
                        // Add text annotation for incomplete attendance
                        annotations[`incomplete-${day}`] = {
                            type: 'label',
                            xValue: index,
                            yValue: (actualHours + expectedHours) / 2, // Middle of the missing hours area
                            content: 'NO\nCHECK-OUT',
                            color: '#f59e0b',
                            font: {
                                size: 10,
                                weight: 'bold'
                            },
                            textAlign: 'center',
                            rotation: 90, // Vertical text
                            backgroundColor: 'rgba(255, 255, 255, 0.8)',
                            borderColor: '#f59e0b',
                            borderWidth: 1,
                            borderRadius: 3,
                            padding: 2
                        };
                    }
                });

                return annotations;
            }).value
        },
        tooltip: {
            callbacks: {
                title: function(context) {
                    return context[0].label;
                },
                label: function(context) {
                    const day = context.label;
                    const datasetLabel = context.dataset.label;
                    const value = context.raw;

                    if (value === 0) return null; // Don't show zero values

                    return `${datasetLabel}: ${value}h`;
                },
                afterBody: function(context) {
                    const day = context[0].label;
                    const hasIncomplete = props.incompleteAttendances?.[day] === true;
                    const shiftInfo = props.shiftData?.[day];
                    const actualHours = props.activityData?.[day] || 0;

                    let footer = [];

                    if (hasIncomplete) {
                        footer.push('⚠️ Incomplete attendance');
                    }

                    if (shiftInfo) {
                        footer.push(`Total worked: ${actualHours}h`);
                        footer.push(`Expected: ${shiftInfo.expected_hours}h`);
                        footer.push(`Shift: ${shiftInfo.start_time} - ${shiftInfo.end_time}`);
                        footer.push(`(${shiftInfo.shift_name})`);
                    } else if (actualHours > 0) {
                        footer.push(`Total worked: ${actualHours}h`);
                        footer.push('No shift scheduled');
                    } else {
                        footer.push('No shift scheduled');
                    }

                    return footer;
                }
            }
        }
    },
    scales: {
        y: {
            beginAtZero: true,
            stacked: true,
            ticks: {
                callback: function(value) {
                    return value + 'h';
                }
            },
            grid: {
                color: 'rgba(0, 0, 0, 0.1)',
            }
        },
        x: {
            stacked: true,
            grid: { display: false }
        }
    }
};
</script>

<template>
    <div ref="containerRef" class="w-full h-full relative">
        <Bar ref="chartRef" :data="chartData" :options="chartOptions" />
    </div>
</template>