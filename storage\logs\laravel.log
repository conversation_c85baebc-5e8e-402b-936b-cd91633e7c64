
[2025-07-11 23:06:56] local.INFO: === WEEKLY SHIFT ASSIGNMENTS DEBUG === {"employee_email":"<EMAIL>","query_period":["2025-07-05","2025-07-11"],"total_assignments":1,"assignments":[{"id":1,"shift_id":2,"shift_name":"Night Shift","start_date":"2025-01-01","end_date":null,"shift_times":"21:00:00 - 05:00:00"}]} 
[2025-07-11 23:06:56] local.INFO: === WORKING DAY CHECK: Sat (2025-07-05) === {"is_weekend":true,"has_shift_assignment":true,"assignment_details":{"shift_name":"Night Shift","shift_times":"21:00:00 - 05:00:00","start_date":"2025-01-01","end_date":null},"will_be_working_day":true} 
[2025-07-11 23:06:56] local.INFO: === WORKING DAY CHECK: Sun (2025-07-06) === {"is_weekend":true,"has_shift_assignment":true,"assignment_details":{"shift_name":"Night Shift","shift_times":"21:00:00 - 05:00:00","start_date":"2025-01-01","end_date":null},"will_be_working_day":true} 
[2025-07-11 23:06:56] local.INFO: === WORKING DAY CHECK: Mon (2025-07-07) === {"is_weekend":false,"has_shift_assignment":true,"assignment_details":{"shift_name":"Night Shift","shift_times":"21:00:00 - 05:00:00","start_date":"2025-01-01","end_date":null},"will_be_working_day":true} 
[2025-07-11 23:06:56] local.INFO: === WORKING DAY CHECK: Tue (2025-07-08) === {"is_weekend":false,"has_shift_assignment":true,"assignment_details":{"shift_name":"Night Shift","shift_times":"21:00:00 - 05:00:00","start_date":"2025-01-01","end_date":null},"will_be_working_day":true} 
[2025-07-11 23:06:56] local.INFO: === WORKING DAY CHECK: Wed (2025-07-09) === {"is_weekend":false,"has_shift_assignment":true,"assignment_details":{"shift_name":"Night Shift","shift_times":"21:00:00 - 05:00:00","start_date":"2025-01-01","end_date":null},"will_be_working_day":true} 
[2025-07-11 23:06:56] local.INFO: === WORKING DAY CHECK: Thu (2025-07-10) === {"is_weekend":false,"has_shift_assignment":true,"assignment_details":{"shift_name":"Night Shift","shift_times":"21:00:00 - 05:00:00","start_date":"2025-01-01","end_date":null},"will_be_working_day":true} 
[2025-07-11 23:06:56] local.INFO: === WORKING DAY CHECK: Fri (2025-07-11) === {"is_weekend":false,"has_shift_assignment":true,"assignment_details":{"shift_name":"Night Shift","shift_times":"21:00:00 - 05:00:00","start_date":"2025-01-01","end_date":null},"will_be_working_day":true} 
[2025-07-11 23:06:56] local.INFO: === WEEKLY ATTENDANCE RECORDS DEBUG === {"employee_email":"<EMAIL>","query_period":["2025-07-05 00:00:00","2025-07-11 23:59:59"],"total_records":9,"records":[{"id":141,"check_in_time":"2025-07-11 20:51:00","check_out_time":"2025-07-12 04:34:00","date":"2025-07-11","day":"Fri","status":"on_time","shift_id":2,"shift_name":"Night Shift"},{"id":136,"check_in_time":"2025-07-10 20:57:00","check_out_time":"2025-07-11 06:44:00","date":"2025-07-10","day":"Thu","status":"on_time","shift_id":2,"shift_name":"Night Shift"},{"id":140,"check_in_time":"2025-07-10 20:57:00","check_out_time":"2025-07-11 04:56:00","date":"2025-07-10","day":"Thu","status":"on_time","shift_id":2,"shift_name":"Night Shift"},{"id":139,"check_in_time":"2025-07-09 21:08:00","check_out_time":null,"date":"2025-07-09","day":"Wed","status":"on_time","shift_id":2,"shift_name":"Night Shift"},{"id":135,"check_in_time":"2025-07-09 21:01:00","check_out_time":"2025-07-10 04:40:00","date":"2025-07-09","day":"Wed","status":"on_time","shift_id":2,"shift_name":"Night Shift"},{"id":138,"check_in_time":"2025-07-08 21:13:00","check_out_time":"2025-07-09 04:43:00","date":"2025-07-08","day":"Tue","status":"on_time","shift_id":2,"shift_name":"Night Shift"},{"id":134,"check_in_time":"2025-07-08 21:00:00","check_out_time":"2025-07-09 06:30:00","date":"2025-07-08","day":"Tue","status":"on_time","shift_id":2,"shift_name":"Night Shift"},{"id":133,"check_in_time":"2025-07-07 21:09:00","check_out_time":"2025-07-08 04:45:00","date":"2025-07-07","day":"Mon","status":"on_time","shift_id":2,"shift_name":"Night Shift"},{"id":137,"check_in_time":"2025-07-07 21:00:00","check_out_time":null,"date":"2025-07-07","day":"Mon","status":"on_time","shift_id":2,"shift_name":"Night Shift"}]} 
[2025-07-11 23:06:56] local.INFO: === DAY CALCULATION DEBUG: Fri (2025-07-11) === {"attendance_id":141,"check_in":"2025-07-11 20:51:00","check_out":"2025-07-12 04:34:00","is_incomplete":false,"status":"on_time","shift_name":"Night Shift"} 
[2025-07-11 23:06:56] local.INFO: Complete attendance calculation {"day":"Fri","minutes_actual":463.0,"hours_actual":7.72} 
[2025-07-11 23:06:56] local.INFO: === DAY CALCULATION DEBUG: Thu (2025-07-10) === {"attendance_id":136,"check_in":"2025-07-10 20:57:00","check_out":"2025-07-11 06:44:00","is_incomplete":false,"status":"on_time","shift_name":"Night Shift"} 
[2025-07-11 23:06:56] local.INFO: Complete attendance calculation {"day":"Thu","minutes_actual":587.0,"hours_actual":9.78} 
[2025-07-11 23:06:56] local.INFO: === DAY CALCULATION DEBUG: Wed (2025-07-09) === {"attendance_id":139,"check_in":"2025-07-09 21:08:00","check_out":null,"is_incomplete":true,"status":"on_time","shift_name":"Night Shift"} 
[2025-07-11 23:06:56] local.INFO: Incomplete - Past day with shift calculation {"day":"Wed","shift_name":"Night Shift","shift_start":"21:00:00","shift_end":"05:00:00","spans_two_days":1,"minutes_estimated":480.0,"hours_estimated":8.0} 
[2025-07-11 23:06:56] local.INFO: === DAY CALCULATION DEBUG: Tue (2025-07-08) === {"attendance_id":138,"check_in":"2025-07-08 21:13:00","check_out":"2025-07-09 04:43:00","is_incomplete":false,"status":"on_time","shift_name":"Night Shift"} 
[2025-07-11 23:06:56] local.INFO: Complete attendance calculation {"day":"Tue","minutes_actual":450.0,"hours_actual":7.5} 
[2025-07-11 23:06:56] local.INFO: === DAY CALCULATION DEBUG: Mon (2025-07-07) === {"attendance_id":133,"check_in":"2025-07-07 21:09:00","check_out":"2025-07-08 04:45:00","is_incomplete":false,"status":"on_time","shift_name":"Night Shift"} 
[2025-07-11 23:06:56] local.INFO: Complete attendance calculation {"day":"Mon","minutes_actual":456.0,"hours_actual":7.6} 
[2025-07-11 23:06:56] local.INFO: === INCOMPLETE ATTENDANCE DEBUG === {"employee_email":"<EMAIL>","query_period":["2025-07-05 00:00:00","2025-07-11 23:59:59"],"incomplete_records_count":2,"incomplete_records":[{"id":137,"check_in_time":"2025-07-07 21:00:00","check_out_time":null,"date":"2025-07-07","day":"Mon"},{"id":139,"check_in_time":"2025-07-09 21:08:00","check_out_time":null,"date":"2025-07-09","day":"Wed"}]} 
[2025-07-11 23:37:42] local.INFO: === INCOMPLETE ATTENDANCE DEBUG === {"week_start":"2025-07-05 00:00:00","week_end":"2025-07-11 23:59:59","working_days":["Sat","Sun","Mon","Tue","Wed","Thu","Fri"],"all_weekly_records_count":9,"incomplete_records_raw":[{"id":137,"date":"2025-07-07","day":"Mon","check_out_time":"NULL"},{"id":139,"date":"2025-07-09","day":"Wed","check_out_time":"NULL"}],"incomplete_attendances_by_day":{"Sat":false,"Sun":false,"Mon":false,"Tue":false,"Wed":false,"Thu":false,"Fri":false}} 
[2025-07-11 23:38:37] local.INFO: Day analysis for 2025-07-07 {"day":"Mon","records_count":2,"records":[{"id":133,"check_out_time":"HAS_CHECKOUT"},{"id":137,"check_out_time":"NULL"}],"all_incomplete":false} 
[2025-07-11 23:38:37] local.INFO: Day analysis for 2025-07-08 {"day":"Tue","records_count":2,"records":[{"id":134,"check_out_time":"HAS_CHECKOUT"},{"id":138,"check_out_time":"HAS_CHECKOUT"}],"all_incomplete":false} 
[2025-07-11 23:38:37] local.INFO: Day analysis for 2025-07-09 {"day":"Wed","records_count":2,"records":[{"id":135,"check_out_time":"HAS_CHECKOUT"},{"id":139,"check_out_time":"NULL"}],"all_incomplete":false} 
[2025-07-11 23:38:37] local.INFO: Day analysis for 2025-07-10 {"day":"Thu","records_count":2,"records":[{"id":136,"check_out_time":"HAS_CHECKOUT"},{"id":140,"check_out_time":"HAS_CHECKOUT"}],"all_incomplete":false} 
[2025-07-11 23:38:37] local.INFO: Day analysis for 2025-07-11 {"day":"Fri","records_count":1,"records":[{"id":141,"check_out_time":"HAS_CHECKOUT"}],"all_incomplete":false} 
[2025-07-11 23:38:37] local.INFO: === INCOMPLETE ATTENDANCE DEBUG === {"week_start":"2025-07-05 00:00:00","week_end":"2025-07-11 23:59:59","working_days":["Sat","Sun","Mon","Tue","Wed","Thu","Fri"],"all_weekly_records_count":9,"incomplete_records_raw":[{"id":137,"date":"2025-07-07","day":"Mon","check_out_time":"NULL"},{"id":139,"date":"2025-07-09","day":"Wed","check_out_time":"NULL"}],"incomplete_attendances_by_day":{"Sat":false,"Sun":false,"Mon":false,"Tue":false,"Wed":false,"Thu":false,"Fri":false}} 
[2025-07-11 23:39:28] local.INFO: Day analysis for 2025-07-07 {"day":"Mon","records_count":2,"latest_record_id":133,"latest_has_checkout":"HAS_CHECKOUT","is_incomplete":false} 
[2025-07-11 23:39:28] local.INFO: Day analysis for 2025-07-08 {"day":"Tue","records_count":2,"latest_record_id":134,"latest_has_checkout":"HAS_CHECKOUT","is_incomplete":false} 
[2025-07-11 23:39:28] local.INFO: Day analysis for 2025-07-09 {"day":"Wed","records_count":2,"latest_record_id":135,"latest_has_checkout":"HAS_CHECKOUT","is_incomplete":false} 
[2025-07-11 23:39:28] local.INFO: Day analysis for 2025-07-10 {"day":"Thu","records_count":2,"latest_record_id":136,"latest_has_checkout":"HAS_CHECKOUT","is_incomplete":false} 
[2025-07-11 23:39:28] local.INFO: Day analysis for 2025-07-11 {"day":"Fri","records_count":1,"latest_record_id":141,"latest_has_checkout":"HAS_CHECKOUT","is_incomplete":false} 
[2025-07-11 23:39:28] local.INFO: === INCOMPLETE ATTENDANCE DEBUG === {"week_start":"2025-07-05 00:00:00","week_end":"2025-07-11 23:59:59","working_days":["Sat","Sun","Mon","Tue","Wed","Thu","Fri"],"all_weekly_records_count":9,"incomplete_records_raw":[{"id":137,"date":"2025-07-07","day":"Mon","check_out_time":"NULL"},{"id":139,"date":"2025-07-09","day":"Wed","check_out_time":"NULL"}],"incomplete_attendances_by_day":{"Sat":false,"Sun":false,"Mon":false,"Tue":false,"Wed":false,"Thu":false,"Fri":false}} 
[2025-07-11 23:40:16] local.INFO: Day analysis for 2025-07-11 {"day":"Fri","records_count":1,"latest_record_id":141,"latest_has_checkout":"HAS_CHECKOUT","is_incomplete":false} 
[2025-07-11 23:40:16] local.INFO: Day analysis for 2025-07-10 {"day":"Thu","records_count":2,"latest_record_id":136,"latest_has_checkout":"HAS_CHECKOUT","is_incomplete":false} 
[2025-07-11 23:40:16] local.INFO: Day analysis for 2025-07-09 {"day":"Wed","records_count":2,"latest_record_id":139,"latest_has_checkout":"NULL","is_incomplete":true} 
[2025-07-11 23:40:16] local.INFO: Day analysis for 2025-07-08 {"day":"Tue","records_count":2,"latest_record_id":138,"latest_has_checkout":"HAS_CHECKOUT","is_incomplete":false} 
[2025-07-11 23:40:16] local.INFO: Day analysis for 2025-07-07 {"day":"Mon","records_count":2,"latest_record_id":133,"latest_has_checkout":"HAS_CHECKOUT","is_incomplete":false} 
[2025-07-11 23:40:16] local.INFO: === INCOMPLETE ATTENDANCE DEBUG === {"week_start":"2025-07-05 00:00:00","week_end":"2025-07-11 23:59:59","working_days":["Sat","Sun","Mon","Tue","Wed","Thu","Fri"],"all_weekly_records_count":9,"incomplete_records_raw":[{"id":137,"date":"2025-07-07","day":"Mon","check_out_time":"NULL"},{"id":139,"date":"2025-07-09","day":"Wed","check_out_time":"NULL"}],"incomplete_attendances_by_day":{"Sat":false,"Sun":false,"Mon":false,"Tue":false,"Wed":true,"Thu":false,"Fri":false}} 
[2025-07-12 00:06:13] local.ERROR: Undefined variable $mondayRecords {"userId":2,"exception":"[object] (ErrorException(code: 0): Undefined variable $mondayRecords at C:\\Users\\<USER>\\Desktop\\projects\\attendancetrackerv2A\\app\\Http\\Controllers\\EmployeeDashboardController.php:320)
[stacktrace]
#0 C:\\Users\\<USER>\\Desktop\\projects\\attendancetrackerv2A\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(256): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleError(2, 'Undefined varia...', 'C:\\\\Users\\\\<USER>\\\\D...', 320)
#1 C:\\Users\\<USER>\\Desktop\\projects\\attendancetrackerv2A\\app\\Http\\Controllers\\EmployeeDashboardController.php(320): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->{closure:Illuminate\\Foundation\\Bootstrap\\HandleExceptions::forwardsTo():255}(2, 'Undefined varia...', 'C:\\\\Users\\\\<USER>\\\\D...', 320)
#2 C:\\Users\\<USER>\\Desktop\\projects\\attendancetrackerv2A\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(46): App\\Http\\Controllers\\EmployeeDashboardController->index(Object(Illuminate\\Http\\Request))
#3 C:\\Users\\<USER>\\Desktop\\projects\\attendancetrackerv2A\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(265): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Http\\Controllers\\EmployeeDashboardController), 'index')
#4 C:\\Users\\<USER>\\Desktop\\projects\\attendancetrackerv2A\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(211): Illuminate\\Routing\\Route->runController()
#5 C:\\Users\\<USER>\\Desktop\\projects\\attendancetrackerv2A\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(808): Illuminate\\Routing\\Route->run()
#6 C:\\Users\\<USER>\\Desktop\\projects\\attendancetrackerv2A\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Routing\\Router->{closure:Illuminate\\Routing\\Router::runRouteWithinStack():807}(Object(Illuminate\\Http\\Request))
#7 C:\\Users\\<USER>\\Desktop\\projects\\attendancetrackerv2A\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php(41): Illuminate\\Pipeline\\Pipeline->{closure:Illuminate\\Pipeline\\Pipeline::prepareDestination():167}(Object(Illuminate\\Http\\Request))
#8 C:\\Users\\<USER>\\Desktop\\projects\\attendancetrackerv2A\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#9 C:\\Users\\<USER>\\Desktop\\projects\\attendancetrackerv2A\\app\\Http\\Middleware\\HandleAppearance.php(21): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#10 C:\\Users\\<USER>\\Desktop\\projects\\attendancetrackerv2A\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): App\\Http\\Middleware\\HandleAppearance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#11 C:\\Users\\<USER>\\Desktop\\projects\\attendancetrackerv2A\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\AddLinkHeadersForPreloadedAssets.php(32): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#12 C:\\Users\\<USER>\\Desktop\\projects\\attendancetrackerv2A\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\AddLinkHeadersForPreloadedAssets->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#13 C:\\Users\\<USER>\\Desktop\\projects\\attendancetrackerv2A\\vendor\\inertiajs\\inertia-laravel\\src\\Middleware.php(86): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#14 C:\\Users\\<USER>\\Desktop\\projects\\attendancetrackerv2A\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Inertia\\Middleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#15 C:\\Users\\<USER>\\Desktop\\projects\\attendancetrackerv2A\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#16 C:\\Users\\<USER>\\Desktop\\projects\\attendancetrackerv2A\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#17 C:\\Users\\<USER>\\Desktop\\projects\\attendancetrackerv2A\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(63): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#18 C:\\Users\\<USER>\\Desktop\\projects\\attendancetrackerv2A\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Auth\\Middleware\\Authenticate->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#19 C:\\Users\\<USER>\\Desktop\\projects\\attendancetrackerv2A\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(87): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#20 C:\\Users\\<USER>\\Desktop\\projects\\attendancetrackerv2A\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#21 C:\\Users\\<USER>\\Desktop\\projects\\attendancetrackerv2A\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(48): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#22 C:\\Users\\<USER>\\Desktop\\projects\\attendancetrackerv2A\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#23 C:\\Users\\<USER>\\Desktop\\projects\\attendancetrackerv2A\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(120): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#24 C:\\Users\\<USER>\\Desktop\\projects\\attendancetrackerv2A\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(63): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#25 C:\\Users\\<USER>\\Desktop\\projects\\attendancetrackerv2A\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#26 C:\\Users\\<USER>\\Desktop\\projects\\attendancetrackerv2A\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(36): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#27 C:\\Users\\<USER>\\Desktop\\projects\\attendancetrackerv2A\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#28 C:\\Users\\<USER>\\Desktop\\projects\\attendancetrackerv2A\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(74): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#29 C:\\Users\\<USER>\\Desktop\\projects\\attendancetrackerv2A\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#30 C:\\Users\\<USER>\\Desktop\\projects\\attendancetrackerv2A\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#31 C:\\Users\\<USER>\\Desktop\\projects\\attendancetrackerv2A\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#32 C:\\Users\\<USER>\\Desktop\\projects\\attendancetrackerv2A\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#33 C:\\Users\\<USER>\\Desktop\\projects\\attendancetrackerv2A\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#34 C:\\Users\\<USER>\\Desktop\\projects\\attendancetrackerv2A\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#35 C:\\Users\\<USER>\\Desktop\\projects\\attendancetrackerv2A\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#36 C:\\Users\\<USER>\\Desktop\\projects\\attendancetrackerv2A\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Foundation\\Http\\Kernel->{closure:Illuminate\\Foundation\\Http\\Kernel::dispatchToRouter():197}(Object(Illuminate\\Http\\Request))
#37 C:\\Users\\<USER>\\Desktop\\projects\\attendancetrackerv2A\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->{closure:Illuminate\\Pipeline\\Pipeline::prepareDestination():167}(Object(Illuminate\\Http\\Request))
#38 C:\\Users\\<USER>\\Desktop\\projects\\attendancetrackerv2A\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#39 C:\\Users\\<USER>\\Desktop\\projects\\attendancetrackerv2A\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#40 C:\\Users\\<USER>\\Desktop\\projects\\attendancetrackerv2A\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#41 C:\\Users\\<USER>\\Desktop\\projects\\attendancetrackerv2A\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#42 C:\\Users\\<USER>\\Desktop\\projects\\attendancetrackerv2A\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#43 C:\\Users\\<USER>\\Desktop\\projects\\attendancetrackerv2A\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#44 C:\\Users\\<USER>\\Desktop\\projects\\attendancetrackerv2A\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#45 C:\\Users\\<USER>\\Desktop\\projects\\attendancetrackerv2A\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#46 C:\\Users\\<USER>\\Desktop\\projects\\attendancetrackerv2A\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#47 C:\\Users\\<USER>\\Desktop\\projects\\attendancetrackerv2A\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(48): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#48 C:\\Users\\<USER>\\Desktop\\projects\\attendancetrackerv2A\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#49 C:\\Users\\<USER>\\Desktop\\projects\\attendancetrackerv2A\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#50 C:\\Users\\<USER>\\Desktop\\projects\\attendancetrackerv2A\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#51 C:\\Users\\<USER>\\Desktop\\projects\\attendancetrackerv2A\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#52 C:\\Users\\<USER>\\Desktop\\projects\\attendancetrackerv2A\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#53 C:\\Users\\<USER>\\Desktop\\projects\\attendancetrackerv2A\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePathEncoding.php(26): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#54 C:\\Users\\<USER>\\Desktop\\projects\\attendancetrackerv2A\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePathEncoding->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#55 C:\\Users\\<USER>\\Desktop\\projects\\attendancetrackerv2A\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#56 C:\\Users\\<USER>\\Desktop\\projects\\attendancetrackerv2A\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#57 C:\\Users\\<USER>\\Desktop\\projects\\attendancetrackerv2A\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#58 C:\\Users\\<USER>\\Desktop\\projects\\attendancetrackerv2A\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1219): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#59 C:\\Users\\<USER>\\Desktop\\projects\\attendancetrackerv2A\\public\\index.php(20): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#60 C:\\Users\\<USER>\\Desktop\\projects\\attendancetrackerv2A\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(23): require_once('C:\\\\Users\\\\<USER>\\\\D...')
#61 {main}
"} 
[2025-07-12 00:06:39] local.INFO: === WEEKLY ACTIVITY DEBUG === {"weekly_start_date":"2025-07-06","weekly_end_date":"2025-07-12","weekly_query_start":"2025-07-06 00:00:00","weekly_query_end":"2025-07-12 23:59:59","working_days":["Sun","Mon","Tue","Wed","Thu","Fri","Sat"],"incomplete_attendances":{"Sun":false,"Mon":false,"Tue":false,"Wed":true,"Thu":false,"Fri":false,"Sat":false},"weekly_activity_data":{"Sun":0,"Mon":7.6,"Tue":7.5,"Wed":0.5,"Thu":9.78,"Fri":7.72,"Sat":0}} 
[2025-07-12 00:06:39] local.ERROR: Undefined variable $mondayRecords {"userId":2,"exception":"[object] (ErrorException(code: 0): Undefined variable $mondayRecords at C:\\Users\\<USER>\\Desktop\\projects\\attendancetrackerv2A\\app\\Http\\Controllers\\EmployeeDashboardController.php:331)
[stacktrace]
#0 C:\\Users\\<USER>\\Desktop\\projects\\attendancetrackerv2A\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(256): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleError(2, 'Undefined varia...', 'C:\\\\Users\\\\<USER>\\\\D...', 331)
#1 C:\\Users\\<USER>\\Desktop\\projects\\attendancetrackerv2A\\app\\Http\\Controllers\\EmployeeDashboardController.php(331): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->{closure:Illuminate\\Foundation\\Bootstrap\\HandleExceptions::forwardsTo():255}(2, 'Undefined varia...', 'C:\\\\Users\\\\<USER>\\\\D...', 331)
#2 C:\\Users\\<USER>\\Desktop\\projects\\attendancetrackerv2A\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(46): App\\Http\\Controllers\\EmployeeDashboardController->index(Object(Illuminate\\Http\\Request))
#3 C:\\Users\\<USER>\\Desktop\\projects\\attendancetrackerv2A\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(265): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Http\\Controllers\\EmployeeDashboardController), 'index')
#4 C:\\Users\\<USER>\\Desktop\\projects\\attendancetrackerv2A\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(211): Illuminate\\Routing\\Route->runController()
#5 C:\\Users\\<USER>\\Desktop\\projects\\attendancetrackerv2A\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(808): Illuminate\\Routing\\Route->run()
#6 C:\\Users\\<USER>\\Desktop\\projects\\attendancetrackerv2A\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Routing\\Router->{closure:Illuminate\\Routing\\Router::runRouteWithinStack():807}(Object(Illuminate\\Http\\Request))
#7 C:\\Users\\<USER>\\Desktop\\projects\\attendancetrackerv2A\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php(41): Illuminate\\Pipeline\\Pipeline->{closure:Illuminate\\Pipeline\\Pipeline::prepareDestination():167}(Object(Illuminate\\Http\\Request))
#8 C:\\Users\\<USER>\\Desktop\\projects\\attendancetrackerv2A\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#9 C:\\Users\\<USER>\\Desktop\\projects\\attendancetrackerv2A\\app\\Http\\Middleware\\HandleAppearance.php(21): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#10 C:\\Users\\<USER>\\Desktop\\projects\\attendancetrackerv2A\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): App\\Http\\Middleware\\HandleAppearance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#11 C:\\Users\\<USER>\\Desktop\\projects\\attendancetrackerv2A\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\AddLinkHeadersForPreloadedAssets.php(32): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#12 C:\\Users\\<USER>\\Desktop\\projects\\attendancetrackerv2A\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\AddLinkHeadersForPreloadedAssets->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#13 C:\\Users\\<USER>\\Desktop\\projects\\attendancetrackerv2A\\vendor\\inertiajs\\inertia-laravel\\src\\Middleware.php(86): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#14 C:\\Users\\<USER>\\Desktop\\projects\\attendancetrackerv2A\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Inertia\\Middleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#15 C:\\Users\\<USER>\\Desktop\\projects\\attendancetrackerv2A\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#16 C:\\Users\\<USER>\\Desktop\\projects\\attendancetrackerv2A\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#17 C:\\Users\\<USER>\\Desktop\\projects\\attendancetrackerv2A\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(63): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#18 C:\\Users\\<USER>\\Desktop\\projects\\attendancetrackerv2A\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Auth\\Middleware\\Authenticate->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#19 C:\\Users\\<USER>\\Desktop\\projects\\attendancetrackerv2A\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(87): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#20 C:\\Users\\<USER>\\Desktop\\projects\\attendancetrackerv2A\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#21 C:\\Users\\<USER>\\Desktop\\projects\\attendancetrackerv2A\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(48): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#22 C:\\Users\\<USER>\\Desktop\\projects\\attendancetrackerv2A\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#23 C:\\Users\\<USER>\\Desktop\\projects\\attendancetrackerv2A\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(120): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#24 C:\\Users\\<USER>\\Desktop\\projects\\attendancetrackerv2A\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(63): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#25 C:\\Users\\<USER>\\Desktop\\projects\\attendancetrackerv2A\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#26 C:\\Users\\<USER>\\Desktop\\projects\\attendancetrackerv2A\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(36): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#27 C:\\Users\\<USER>\\Desktop\\projects\\attendancetrackerv2A\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#28 C:\\Users\\<USER>\\Desktop\\projects\\attendancetrackerv2A\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(74): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#29 C:\\Users\\<USER>\\Desktop\\projects\\attendancetrackerv2A\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#30 C:\\Users\\<USER>\\Desktop\\projects\\attendancetrackerv2A\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#31 C:\\Users\\<USER>\\Desktop\\projects\\attendancetrackerv2A\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#32 C:\\Users\\<USER>\\Desktop\\projects\\attendancetrackerv2A\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#33 C:\\Users\\<USER>\\Desktop\\projects\\attendancetrackerv2A\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#34 C:\\Users\\<USER>\\Desktop\\projects\\attendancetrackerv2A\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#35 C:\\Users\\<USER>\\Desktop\\projects\\attendancetrackerv2A\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#36 C:\\Users\\<USER>\\Desktop\\projects\\attendancetrackerv2A\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Foundation\\Http\\Kernel->{closure:Illuminate\\Foundation\\Http\\Kernel::dispatchToRouter():197}(Object(Illuminate\\Http\\Request))
#37 C:\\Users\\<USER>\\Desktop\\projects\\attendancetrackerv2A\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->{closure:Illuminate\\Pipeline\\Pipeline::prepareDestination():167}(Object(Illuminate\\Http\\Request))
#38 C:\\Users\\<USER>\\Desktop\\projects\\attendancetrackerv2A\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#39 C:\\Users\\<USER>\\Desktop\\projects\\attendancetrackerv2A\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#40 C:\\Users\\<USER>\\Desktop\\projects\\attendancetrackerv2A\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#41 C:\\Users\\<USER>\\Desktop\\projects\\attendancetrackerv2A\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#42 C:\\Users\\<USER>\\Desktop\\projects\\attendancetrackerv2A\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#43 C:\\Users\\<USER>\\Desktop\\projects\\attendancetrackerv2A\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#44 C:\\Users\\<USER>\\Desktop\\projects\\attendancetrackerv2A\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#45 C:\\Users\\<USER>\\Desktop\\projects\\attendancetrackerv2A\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#46 C:\\Users\\<USER>\\Desktop\\projects\\attendancetrackerv2A\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#47 C:\\Users\\<USER>\\Desktop\\projects\\attendancetrackerv2A\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(48): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#48 C:\\Users\\<USER>\\Desktop\\projects\\attendancetrackerv2A\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#49 C:\\Users\\<USER>\\Desktop\\projects\\attendancetrackerv2A\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#50 C:\\Users\\<USER>\\Desktop\\projects\\attendancetrackerv2A\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#51 C:\\Users\\<USER>\\Desktop\\projects\\attendancetrackerv2A\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#52 C:\\Users\\<USER>\\Desktop\\projects\\attendancetrackerv2A\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#53 C:\\Users\\<USER>\\Desktop\\projects\\attendancetrackerv2A\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePathEncoding.php(26): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#54 C:\\Users\\<USER>\\Desktop\\projects\\attendancetrackerv2A\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePathEncoding->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#55 C:\\Users\\<USER>\\Desktop\\projects\\attendancetrackerv2A\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#56 C:\\Users\\<USER>\\Desktop\\projects\\attendancetrackerv2A\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#57 C:\\Users\\<USER>\\Desktop\\projects\\attendancetrackerv2A\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#58 C:\\Users\\<USER>\\Desktop\\projects\\attendancetrackerv2A\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1219): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#59 C:\\Users\\<USER>\\Desktop\\projects\\attendancetrackerv2A\\public\\index.php(20): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#60 C:\\Users\\<USER>\\Desktop\\projects\\attendancetrackerv2A\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(23): require_once('C:\\\\Users\\\\<USER>\\\\D...')
#61 {main}
"} 
[2025-07-12 00:07:22] local.INFO: === WEEKLY ACTIVITY DEBUG === {"weekly_start_date":"2025-07-06","weekly_end_date":"2025-07-12","weekly_query_start":"2025-07-06 00:00:00","weekly_query_end":"2025-07-12 23:59:59","working_days":["Sun","Mon","Tue","Wed","Thu","Fri","Sat"],"incomplete_attendances":{"Sun":false,"Mon":false,"Tue":false,"Wed":true,"Thu":false,"Fri":false,"Sat":false},"weekly_activity_data":{"Sun":0,"Mon":7.6,"Tue":7.5,"Wed":0.5,"Thu":9.78,"Fri":7.72,"Sat":0}} 
[2025-07-12 00:07:48] local.INFO: === WEEKLY ACTIVITY DEBUG === {"weekly_start_date":"2025-06-29","weekly_end_date":"2025-07-12","weekly_query_start":"2025-06-29 00:00:00","weekly_query_end":"2025-07-12 23:59:59","working_days":["Sun","Mon","Tue","Wed","Thu","Fri","Sat","Sun","Mon","Tue","Wed","Thu","Fri","Sat"],"incomplete_attendances":{"Sun":false,"Mon":false,"Tue":false,"Wed":true,"Thu":false,"Fri":false,"Sat":false},"weekly_activity_data":{"Sun":0,"Mon":10.87,"Tue":10.38,"Wed":7.82,"Thu":10.13,"Fri":11.07,"Sat":0}} 
[2025-07-12 00:17:55] local.INFO: === PARAMETER PRECEDENCE DEBUG === {"request_params":[],"month_from_request":null,"year_from_request":null,"weekly_start_from_request":null,"weekly_end_from_request":null,"final_weekly_start":"2025-07-06","final_weekly_end":"2025-07-12"} 
[2025-07-12 00:17:55] local.INFO: === REQUEST PARAMETERS DEBUG === {"all_request_params":[],"weekly_start_date_param":null,"weekly_end_date_param":null,"year_param":null,"month_param":null,"calculated_weekly_start":"2025-07-06","calculated_weekly_end":"2025-07-12"} 
[2025-07-12 00:17:55] local.INFO: === NIGHT SHIFT DATE BOUNDARY DEBUG === {"weekly_query_range":{"start":"2025-07-06 00:00:00","end":"2025-07-12 23:59:59"},"attendance_records":[{"id":141,"check_in_date":"2025-07-11","check_in_time":"2025-07-11 20:51:00","check_out_date":"2025-07-12","check_out_time":"2025-07-12 04:34:00","spans_two_days":true,"shift_name":"Night Shift"},{"id":136,"check_in_date":"2025-07-10","check_in_time":"2025-07-10 20:57:00","check_out_date":"2025-07-11","check_out_time":"2025-07-11 06:44:00","spans_two_days":true,"shift_name":"Night Shift"},{"id":140,"check_in_date":"2025-07-10","check_in_time":"2025-07-10 20:57:00","check_out_date":"2025-07-11","check_out_time":"2025-07-11 04:56:00","spans_two_days":true,"shift_name":"Night Shift"},{"id":139,"check_in_date":"2025-07-09","check_in_time":"2025-07-09 21:08:00","check_out_date":null,"check_out_time":"NULL","spans_two_days":false,"shift_name":"Night Shift"},{"id":135,"check_in_date":"2025-07-09","check_in_time":"2025-07-09 21:01:00","check_out_date":"2025-07-10","check_out_time":"2025-07-10 04:40:00","spans_two_days":true,"shift_name":"Night Shift"},{"id":138,"check_in_date":"2025-07-08","check_in_time":"2025-07-08 21:13:00","check_out_date":"2025-07-09","check_out_time":"2025-07-09 04:43:00","spans_two_days":true,"shift_name":"Night Shift"},{"id":134,"check_in_date":"2025-07-08","check_in_time":"2025-07-08 21:00:00","check_out_date":"2025-07-09","check_out_time":"2025-07-09 06:30:00","spans_two_days":true,"shift_name":"Night Shift"},{"id":133,"check_in_date":"2025-07-07","check_in_time":"2025-07-07 21:09:00","check_out_date":"2025-07-08","check_out_time":"2025-07-08 04:45:00","spans_two_days":true,"shift_name":"Night Shift"},{"id":137,"check_in_date":"2025-07-07","check_in_time":"2025-07-07 21:00:00","check_out_date":null,"check_out_time":"NULL","spans_two_days":false,"shift_name":"Night Shift"}]} 
[2025-07-12 00:17:55] local.INFO: === DAY PROCESSING DEBUG: Fri (2025-07-11) === {"latest_attendance_id":141,"check_in_time":"2025-07-11 20:51:00","check_out_time":"2025-07-12 04:34:00","is_incomplete":false,"total_attendances_for_day":1} 
[2025-07-12 00:17:55] local.INFO: === DAY PROCESSING DEBUG: Thu (2025-07-10) === {"latest_attendance_id":136,"check_in_time":"2025-07-10 20:57:00","check_out_time":"2025-07-11 06:44:00","is_incomplete":false,"total_attendances_for_day":2} 
[2025-07-12 00:17:55] local.INFO: === DAY PROCESSING DEBUG: Wed (2025-07-09) === {"latest_attendance_id":139,"check_in_time":"2025-07-09 21:08:00","check_out_time":"NULL","is_incomplete":true,"total_attendances_for_day":2} 
[2025-07-12 00:17:55] local.INFO: === DAY PROCESSING DEBUG: Tue (2025-07-08) === {"latest_attendance_id":138,"check_in_time":"2025-07-08 21:13:00","check_out_time":"2025-07-09 04:43:00","is_incomplete":false,"total_attendances_for_day":2} 
[2025-07-12 00:17:55] local.INFO: === DAY PROCESSING DEBUG: Mon (2025-07-07) === {"latest_attendance_id":133,"check_in_time":"2025-07-07 21:09:00","check_out_time":"2025-07-08 04:45:00","is_incomplete":false,"total_attendances_for_day":2} 
[2025-07-12 00:17:55] local.INFO: === INCOMPLETE DETECTION DEBUG for 2025-07-11 === {"date":"2025-07-11","total_records_for_day":1,"latest_record_id":141,"latest_check_in":"2025-07-11 20:51:00","latest_check_out":"2025-07-12 04:34:00","is_incomplete":false,"all_records_for_day":[{"id":141,"check_in":"2025-07-11 20:51:00","check_out":"2025-07-12 04:34:00"}]} 
[2025-07-12 00:17:55] local.INFO: === INCOMPLETE DETECTION DEBUG for 2025-07-10 === {"date":"2025-07-10","total_records_for_day":2,"latest_record_id":136,"latest_check_in":"2025-07-10 20:57:00","latest_check_out":"2025-07-11 06:44:00","is_incomplete":false,"all_records_for_day":[{"id":136,"check_in":"2025-07-10 20:57:00","check_out":"2025-07-11 06:44:00"},{"id":140,"check_in":"2025-07-10 20:57:00","check_out":"2025-07-11 04:56:00"}]} 
[2025-07-12 00:17:55] local.INFO: === INCOMPLETE DETECTION DEBUG for 2025-07-09 === {"date":"2025-07-09","total_records_for_day":2,"latest_record_id":139,"latest_check_in":"2025-07-09 21:08:00","latest_check_out":"NULL","is_incomplete":true,"all_records_for_day":[{"id":139,"check_in":"2025-07-09 21:08:00","check_out":"NULL"},{"id":135,"check_in":"2025-07-09 21:01:00","check_out":"2025-07-10 04:40:00"}]} 
[2025-07-12 00:17:55] local.INFO: === INCOMPLETE DETECTION DEBUG for 2025-07-08 === {"date":"2025-07-08","total_records_for_day":2,"latest_record_id":138,"latest_check_in":"2025-07-08 21:13:00","latest_check_out":"2025-07-09 04:43:00","is_incomplete":false,"all_records_for_day":[{"id":138,"check_in":"2025-07-08 21:13:00","check_out":"2025-07-09 04:43:00"},{"id":134,"check_in":"2025-07-08 21:00:00","check_out":"2025-07-09 06:30:00"}]} 
[2025-07-12 00:17:55] local.INFO: === INCOMPLETE DETECTION DEBUG for 2025-07-07 === {"date":"2025-07-07","total_records_for_day":2,"latest_record_id":133,"latest_check_in":"2025-07-07 21:09:00","latest_check_out":"2025-07-08 04:45:00","is_incomplete":false,"all_records_for_day":[{"id":133,"check_in":"2025-07-07 21:09:00","check_out":"2025-07-08 04:45:00"},{"id":137,"check_in":"2025-07-07 21:00:00","check_out":"NULL"}]} 
[2025-07-12 00:20:41] local.INFO: === REQUEST PARAMETERS DEBUG === {"all_request_params":[],"weekly_start_date_param":null,"weekly_end_date_param":null,"year_param":null,"month_param":null,"calculated_weekly_start":"2025-07-06","calculated_weekly_end":"2025-07-12"} 
[2025-07-12 00:21:31] local.INFO: === REQUEST PARAMETERS DEBUG === {"all_request_params":[],"weekly_start_date_param":null,"weekly_end_date_param":null,"year_param":null,"month_param":null,"calculated_weekly_start":"2025-07-06","calculated_weekly_end":"2025-07-12"} 
[2025-07-12 00:21:47] local.INFO: === REQUEST PARAMETERS DEBUG === {"all_request_params":[],"weekly_start_date_param":null,"weekly_end_date_param":null,"year_param":null,"month_param":null,"calculated_weekly_start":"2025-07-06","calculated_weekly_end":"2025-07-12"} 
[2025-07-12 00:21:57] local.INFO: === REQUEST PARAMETERS DEBUG === {"all_request_params":[],"weekly_start_date_param":null,"weekly_end_date_param":null,"year_param":null,"month_param":null,"calculated_weekly_start":"2025-07-06","calculated_weekly_end":"2025-07-12"} 
[2025-07-12 00:22:07] local.INFO: === REQUEST PARAMETERS DEBUG === {"all_request_params":[],"weekly_start_date_param":null,"weekly_end_date_param":null,"year_param":null,"month_param":null,"calculated_weekly_start":"2025-07-06","calculated_weekly_end":"2025-07-12"} 
[2025-07-12 00:22:09] local.INFO: === REQUEST PARAMETERS DEBUG === {"all_request_params":[],"weekly_start_date_param":null,"weekly_end_date_param":null,"year_param":null,"month_param":null,"calculated_weekly_start":"2025-07-06","calculated_weekly_end":"2025-07-12"} 
[2025-07-12 00:22:57] local.INFO: === REQUEST PARAMETERS DEBUG === {"all_request_params":[],"weekly_start_date_param":null,"weekly_end_date_param":null,"year_param":null,"month_param":null,"calculated_weekly_start":"2025-07-06","calculated_weekly_end":"2025-07-12"} 
[2025-07-12 00:22:57] local.INFO: === MONDAY JULY 7TH DEBUG === {"all_records_for_day":[{"id":137,"check_in_time":"21:00","check_out_time":"NULL","is_incomplete":true},{"id":133,"check_in_time":"21:09","check_out_time":"04:45","is_incomplete":false}],"selected_latest":{"id":137,"check_in_time":"21:00","check_out_time":"NULL","is_incomplete":true}} 
[2025-07-12 00:23:56] local.INFO: === REQUEST PARAMETERS DEBUG === {"all_request_params":[],"weekly_start_date_param":null,"weekly_end_date_param":null,"year_param":null,"month_param":null,"calculated_weekly_start":"2025-07-06","calculated_weekly_end":"2025-07-12"} 
[2025-07-12 00:23:58] local.INFO: === REQUEST PARAMETERS DEBUG === {"all_request_params":[],"weekly_start_date_param":null,"weekly_end_date_param":null,"year_param":null,"month_param":null,"calculated_weekly_start":"2025-07-06","calculated_weekly_end":"2025-07-12"} 
[2025-07-12 00:24:01] local.INFO: === REQUEST PARAMETERS DEBUG === {"all_request_params":[],"weekly_start_date_param":null,"weekly_end_date_param":null,"year_param":null,"month_param":null,"calculated_weekly_start":"2025-07-06","calculated_weekly_end":"2025-07-12"} 
[2025-07-12 00:24:12] local.INFO: === REQUEST PARAMETERS DEBUG === {"all_request_params":[],"weekly_start_date_param":null,"weekly_end_date_param":null,"year_param":null,"month_param":null,"calculated_weekly_start":"2025-07-06","calculated_weekly_end":"2025-07-12"} 
[2025-07-12 00:24:14] local.INFO: === REQUEST PARAMETERS DEBUG === {"all_request_params":[],"weekly_start_date_param":null,"weekly_end_date_param":null,"year_param":null,"month_param":null,"calculated_weekly_start":"2025-07-06","calculated_weekly_end":"2025-07-12"} 
[2025-07-12 00:24:16] local.INFO: === REQUEST PARAMETERS DEBUG === {"all_request_params":[],"weekly_start_date_param":null,"weekly_end_date_param":null,"year_param":null,"month_param":null,"calculated_weekly_start":"2025-07-06","calculated_weekly_end":"2025-07-12"} 
[2025-07-12 00:24:21] local.INFO: === REQUEST PARAMETERS DEBUG === {"all_request_params":[],"weekly_start_date_param":null,"weekly_end_date_param":null,"year_param":null,"month_param":null,"calculated_weekly_start":"2025-07-06","calculated_weekly_end":"2025-07-12"} 
[2025-07-12 00:25:19] local.INFO: === REQUEST PARAMETERS DEBUG === {"all_request_params":[],"weekly_start_date_param":null,"weekly_end_date_param":null,"year_param":null,"month_param":null,"calculated_weekly_start":"2025-07-06","calculated_weekly_end":"2025-07-12"} 
[2025-07-12 00:25:21] local.INFO: === REQUEST PARAMETERS DEBUG === {"all_request_params":[],"weekly_start_date_param":null,"weekly_end_date_param":null,"year_param":null,"month_param":null,"calculated_weekly_start":"2025-07-06","calculated_weekly_end":"2025-07-12"} 
[2025-07-12 00:25:23] local.INFO: === REQUEST PARAMETERS DEBUG === {"all_request_params":[],"weekly_start_date_param":null,"weekly_end_date_param":null,"year_param":null,"month_param":null,"calculated_weekly_start":"2025-07-06","calculated_weekly_end":"2025-07-12"} 
[2025-07-12 00:25:25] local.INFO: === REQUEST PARAMETERS DEBUG === {"all_request_params":[],"weekly_start_date_param":null,"weekly_end_date_param":null,"year_param":null,"month_param":null,"calculated_weekly_start":"2025-07-06","calculated_weekly_end":"2025-07-12"} 
[2025-07-12 00:25:33] local.INFO: === REQUEST PARAMETERS DEBUG === {"all_request_params":[],"weekly_start_date_param":null,"weekly_end_date_param":null,"year_param":null,"month_param":null,"calculated_weekly_start":"2025-07-06","calculated_weekly_end":"2025-07-12"} 
[2025-07-12 00:25:35] local.INFO: === REQUEST PARAMETERS DEBUG === {"all_request_params":[],"weekly_start_date_param":null,"weekly_end_date_param":null,"year_param":null,"month_param":null,"calculated_weekly_start":"2025-07-06","calculated_weekly_end":"2025-07-12"} 
[2025-07-12 00:25:37] local.INFO: === REQUEST PARAMETERS DEBUG === {"all_request_params":[],"weekly_start_date_param":null,"weekly_end_date_param":null,"year_param":null,"month_param":null,"calculated_weekly_start":"2025-07-06","calculated_weekly_end":"2025-07-12"} 
[2025-07-12 00:25:39] local.INFO: === REQUEST PARAMETERS DEBUG === {"all_request_params":[],"weekly_start_date_param":null,"weekly_end_date_param":null,"year_param":null,"month_param":null,"calculated_weekly_start":"2025-07-06","calculated_weekly_end":"2025-07-12"} 
[2025-07-12 00:25:44] local.INFO: === REQUEST PARAMETERS DEBUG === {"all_request_params":[],"weekly_start_date_param":null,"weekly_end_date_param":null,"year_param":null,"month_param":null,"calculated_weekly_start":"2025-07-06","calculated_weekly_end":"2025-07-12"} 
[2025-07-12 00:26:10] local.INFO: === REQUEST PARAMETERS DEBUG === {"all_request_params":[],"weekly_start_date_param":null,"weekly_end_date_param":null,"year_param":null,"month_param":null,"calculated_weekly_start":"2025-07-06","calculated_weekly_end":"2025-07-12"} 
[2025-07-12 00:26:12] local.INFO: === REQUEST PARAMETERS DEBUG === {"all_request_params":[],"weekly_start_date_param":null,"weekly_end_date_param":null,"year_param":null,"month_param":null,"calculated_weekly_start":"2025-07-06","calculated_weekly_end":"2025-07-12"} 
[2025-07-12 00:26:14] local.INFO: === REQUEST PARAMETERS DEBUG === {"all_request_params":[],"weekly_start_date_param":null,"weekly_end_date_param":null,"year_param":null,"month_param":null,"calculated_weekly_start":"2025-07-06","calculated_weekly_end":"2025-07-12"} 
[2025-07-12 00:26:16] local.INFO: === REQUEST PARAMETERS DEBUG === {"all_request_params":[],"weekly_start_date_param":null,"weekly_end_date_param":null,"year_param":null,"month_param":null,"calculated_weekly_start":"2025-07-06","calculated_weekly_end":"2025-07-12"} 
[2025-07-12 00:26:18] local.INFO: === REQUEST PARAMETERS DEBUG === {"all_request_params":[],"weekly_start_date_param":null,"weekly_end_date_param":null,"year_param":null,"month_param":null,"calculated_weekly_start":"2025-07-06","calculated_weekly_end":"2025-07-12"} 
[2025-07-12 00:26:34] local.INFO: === REQUEST PARAMETERS DEBUG === {"all_request_params":[],"weekly_start_date_param":null,"weekly_end_date_param":null,"year_param":null,"month_param":null,"calculated_weekly_start":"2025-07-06","calculated_weekly_end":"2025-07-12"} 
[2025-07-12 00:26:36] local.INFO: === REQUEST PARAMETERS DEBUG === {"all_request_params":[],"weekly_start_date_param":null,"weekly_end_date_param":null,"year_param":null,"month_param":null,"calculated_weekly_start":"2025-07-06","calculated_weekly_end":"2025-07-12"} 
[2025-07-12 00:26:38] local.INFO: === REQUEST PARAMETERS DEBUG === {"all_request_params":[],"weekly_start_date_param":null,"weekly_end_date_param":null,"year_param":null,"month_param":null,"calculated_weekly_start":"2025-07-06","calculated_weekly_end":"2025-07-12"} 
[2025-07-12 00:26:40] local.INFO: === REQUEST PARAMETERS DEBUG === {"all_request_params":[],"weekly_start_date_param":null,"weekly_end_date_param":null,"year_param":null,"month_param":null,"calculated_weekly_start":"2025-07-06","calculated_weekly_end":"2025-07-12"} 
[2025-07-12 00:26:42] local.INFO: === REQUEST PARAMETERS DEBUG === {"all_request_params":[],"weekly_start_date_param":null,"weekly_end_date_param":null,"year_param":null,"month_param":null,"calculated_weekly_start":"2025-07-06","calculated_weekly_end":"2025-07-12"} 
[2025-07-12 00:26:44] local.INFO: === REQUEST PARAMETERS DEBUG === {"all_request_params":[],"weekly_start_date_param":null,"weekly_end_date_param":null,"year_param":null,"month_param":null,"calculated_weekly_start":"2025-07-06","calculated_weekly_end":"2025-07-12"} 
[2025-07-12 00:27:17] local.INFO: === REQUEST PARAMETERS DEBUG === {"all_request_params":[],"weekly_start_date_param":null,"weekly_end_date_param":null,"year_param":null,"month_param":null,"calculated_weekly_start":"2025-07-06","calculated_weekly_end":"2025-07-12"} 
[2025-07-12 00:27:37] local.INFO: ✅ Date picker working! {"weekly_start_date":"2025-07-06","weekly_end_date":"2025-07-12"} 
[2025-07-12 00:27:37] local.INFO: === REQUEST PARAMETERS DEBUG === {"all_request_params":{"weekly_start_date":"2025-07-06","weekly_end_date":"2025-07-12"},"weekly_start_date_param":"2025-07-06","weekly_end_date_param":"2025-07-12","year_param":null,"month_param":null,"calculated_weekly_start":"2025-07-06","calculated_weekly_end":"2025-07-12"} 
[2025-07-12 00:27:42] local.INFO: ✅ Date picker working! {"weekly_start_date":"2025-07-01","weekly_end_date":"2025-07-07"} 
[2025-07-12 00:27:42] local.INFO: === REQUEST PARAMETERS DEBUG === {"all_request_params":{"weekly_start_date":"2025-07-01","weekly_end_date":"2025-07-07"},"weekly_start_date_param":"2025-07-01","weekly_end_date_param":"2025-07-07","year_param":null,"month_param":null,"calculated_weekly_start":"2025-07-01","calculated_weekly_end":"2025-07-07"} 
[2025-07-12 00:28:55] local.INFO: === REQUEST PARAMETERS DEBUG === {"all_request_params":{"weekly_start_date":"2025-07-01","weekly_end_date":"2025-07-07"},"weekly_start_date_param":"2025-07-01","weekly_end_date_param":"2025-07-07","year_param":null,"month_param":null,"calculated_weekly_start":"2025-07-01","calculated_weekly_end":"2025-07-07"} 
[2025-07-12 00:29:12] local.INFO: === REQUEST PARAMETERS DEBUG === {"all_request_params":{"weekly_start_date":"2025-07-06","weekly_end_date":"2025-07-12"},"weekly_start_date_param":"2025-07-06","weekly_end_date_param":"2025-07-12","year_param":null,"month_param":null,"calculated_weekly_start":"2025-07-06","calculated_weekly_end":"2025-07-12"} 
[2025-07-12 00:29:23] local.INFO: === REQUEST PARAMETERS DEBUG === {"all_request_params":{"weekly_start_date":"2025-07-05","weekly_end_date":"2025-07-11"},"weekly_start_date_param":"2025-07-05","weekly_end_date_param":"2025-07-11","year_param":null,"month_param":null,"calculated_weekly_start":"2025-07-05","calculated_weekly_end":"2025-07-11"} 
[2025-07-12 00:29:59] local.INFO: === REQUEST PARAMETERS DEBUG === {"all_request_params":{"month":"8","year":"2025"},"weekly_start_date_param":null,"weekly_end_date_param":null,"year_param":"2025","month_param":"8","calculated_weekly_start":"2025-07-06","calculated_weekly_end":"2025-07-12"} 
[2025-07-12 00:30:07] local.INFO: === REQUEST PARAMETERS DEBUG === {"all_request_params":{"month":"7","year":"2025"},"weekly_start_date_param":null,"weekly_end_date_param":null,"year_param":"2025","month_param":"7","calculated_weekly_start":"2025-07-06","calculated_weekly_end":"2025-07-12"} 
[2025-07-12 00:30:08] local.INFO: === REQUEST PARAMETERS DEBUG === {"all_request_params":{"month":"6","year":"2025"},"weekly_start_date_param":null,"weekly_end_date_param":null,"year_param":"2025","month_param":"6","calculated_weekly_start":"2025-07-06","calculated_weekly_end":"2025-07-12"} 
[2025-07-12 00:30:10] local.INFO: === REQUEST PARAMETERS DEBUG === {"all_request_params":{"month":"5","year":"2025"},"weekly_start_date_param":null,"weekly_end_date_param":null,"year_param":"2025","month_param":"5","calculated_weekly_start":"2025-07-06","calculated_weekly_end":"2025-07-12"} 
[2025-07-12 00:30:12] local.INFO: === REQUEST PARAMETERS DEBUG === {"all_request_params":{"month":"6","year":"2025"},"weekly_start_date_param":null,"weekly_end_date_param":null,"year_param":"2025","month_param":"6","calculated_weekly_start":"2025-07-06","calculated_weekly_end":"2025-07-12"} 
[2025-07-12 00:30:13] local.INFO: === REQUEST PARAMETERS DEBUG === {"all_request_params":{"month":"7","year":"2025"},"weekly_start_date_param":null,"weekly_end_date_param":null,"year_param":"2025","month_param":"7","calculated_weekly_start":"2025-07-06","calculated_weekly_end":"2025-07-12"} 
[2025-07-12 00:30:15] local.INFO: === REQUEST PARAMETERS DEBUG === {"all_request_params":{"month":"7","year":"2025"},"weekly_start_date_param":null,"weekly_end_date_param":null,"year_param":"2025","month_param":"7","calculated_weekly_start":"2025-07-06","calculated_weekly_end":"2025-07-12"} 
[2025-07-12 00:30:22] local.INFO: === REQUEST PARAMETERS DEBUG === {"all_request_params":{"month":"6","year":"2025"},"weekly_start_date_param":null,"weekly_end_date_param":null,"year_param":"2025","month_param":"6","calculated_weekly_start":"2025-07-06","calculated_weekly_end":"2025-07-12"} 
[2025-07-12 00:30:24] local.INFO: === REQUEST PARAMETERS DEBUG === {"all_request_params":{"month":"7","year":"2025"},"weekly_start_date_param":null,"weekly_end_date_param":null,"year_param":"2025","month_param":"7","calculated_weekly_start":"2025-07-06","calculated_weekly_end":"2025-07-12"} 
[2025-07-12 00:30:27] local.INFO: === REQUEST PARAMETERS DEBUG === {"all_request_params":{"month":"6","year":"2025"},"weekly_start_date_param":null,"weekly_end_date_param":null,"year_param":"2025","month_param":"6","calculated_weekly_start":"2025-07-06","calculated_weekly_end":"2025-07-12"} 
[2025-07-12 00:30:41] local.INFO: === REQUEST PARAMETERS DEBUG === {"all_request_params":{"weekly_start_date":"2025-06-29","weekly_end_date":"2025-07-05"},"weekly_start_date_param":"2025-06-29","weekly_end_date_param":"2025-07-05","year_param":null,"month_param":null,"calculated_weekly_start":"2025-06-29","calculated_weekly_end":"2025-07-05"} 
[2025-07-12 00:31:03] local.INFO: === REQUEST PARAMETERS DEBUG === {"all_request_params":{"weekly_start_date":"2025-06-08","weekly_end_date":"2025-06-14"},"weekly_start_date_param":"2025-06-08","weekly_end_date_param":"2025-06-14","year_param":null,"month_param":null,"calculated_weekly_start":"2025-06-08","calculated_weekly_end":"2025-06-14"} 
[2025-07-12 00:31:14] local.INFO: === REQUEST PARAMETERS DEBUG === {"all_request_params":{"weekly_start_date":"2025-06-16","weekly_end_date":"2025-06-22"},"weekly_start_date_param":"2025-06-16","weekly_end_date_param":"2025-06-22","year_param":null,"month_param":null,"calculated_weekly_start":"2025-06-16","calculated_weekly_end":"2025-06-22"} 
[2025-07-12 00:31:42] local.INFO: === REQUEST PARAMETERS DEBUG === {"all_request_params":{"weekly_start_date":"2025-06-17","weekly_end_date":"2025-06-23"},"weekly_start_date_param":"2025-06-17","weekly_end_date_param":"2025-06-23","year_param":null,"month_param":null,"calculated_weekly_start":"2025-06-17","calculated_weekly_end":"2025-06-23"} 
[2025-07-12 00:32:00] local.INFO: === REQUEST PARAMETERS DEBUG === {"all_request_params":{"weekly_start_date":"2025-06-24","weekly_end_date":"2025-06-30"},"weekly_start_date_param":"2025-06-24","weekly_end_date_param":"2025-06-30","year_param":null,"month_param":null,"calculated_weekly_start":"2025-06-24","calculated_weekly_end":"2025-06-30"} 
[2025-07-12 00:32:14] local.INFO: === REQUEST PARAMETERS DEBUG === {"all_request_params":{"weekly_start_date":"2025-07-01","weekly_end_date":"2025-07-07"},"weekly_start_date_param":"2025-07-01","weekly_end_date_param":"2025-07-07","year_param":null,"month_param":null,"calculated_weekly_start":"2025-07-01","calculated_weekly_end":"2025-07-07"} 
[2025-07-12 00:32:39] local.INFO: === REQUEST PARAMETERS DEBUG === {"all_request_params":{"weekly_start_date":"2025-06-29","weekly_end_date":"2025-07-05"},"weekly_start_date_param":"2025-06-29","weekly_end_date_param":"2025-07-05","year_param":null,"month_param":null,"calculated_weekly_start":"2025-06-29","calculated_weekly_end":"2025-07-05"} 
[2025-07-12 00:32:46] local.INFO: === REQUEST PARAMETERS DEBUG === {"all_request_params":{"weekly_start_date":"2025-06-29","weekly_end_date":"2025-07-05"},"weekly_start_date_param":"2025-06-29","weekly_end_date_param":"2025-07-05","year_param":null,"month_param":null,"calculated_weekly_start":"2025-06-29","calculated_weekly_end":"2025-07-05"} 
[2025-07-12 00:32:57] local.INFO: === REQUEST PARAMETERS DEBUG === {"all_request_params":{"weekly_start_date":"2025-07-06","weekly_end_date":"2025-07-12"},"weekly_start_date_param":"2025-07-06","weekly_end_date_param":"2025-07-12","year_param":null,"month_param":null,"calculated_weekly_start":"2025-07-06","calculated_weekly_end":"2025-07-12"} 
[2025-07-12 00:35:33] local.INFO: === REQUEST PARAMETERS DEBUG === {"all_request_params":[],"weekly_start_date_param":null,"weekly_end_date_param":null,"year_param":null,"month_param":null,"calculated_weekly_start":"2025-07-06","calculated_weekly_end":"2025-07-12"} 
[2025-07-12 00:35:33] local.INFO: July 7 activity calc - Latest attendance ID: 133, Check-in: 2025-07-07 21:09:00, Check-out: 2025-07-08 04:45:00, Is incomplete: NO  
[2025-07-12 00:35:33] local.INFO: July 7 incomplete check - Latest attendance ID: 133, Check-in: 2025-07-07 21:09:00, Check-out: 2025-07-08 04:45:00, Is incomplete: NO  
[2025-07-12 00:41:25] local.ERROR: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near 'check_in_time))' at line 1 (Connection: mysql, SQL: alter table `attendances` add unique `unique_employee_daily_attendance`(`employee_profile_id`, DATE(check_in_time))) {"exception":"[object] (Illuminate\\Database\\QueryException(code: 42000): SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near 'check_in_time))' at line 1 (Connection: mysql, SQL: alter table `attendances` add unique `unique_employee_daily_attendance`(`employee_profile_id`, DATE(check_in_time))) at C:\\Users\\<USER>\\Desktop\\projects\\attendancetrackerv2A\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:822)
[stacktrace]
#0 C:\\Users\\<USER>\\Desktop\\projects\\attendancetrackerv2A\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(776): Illuminate\\Database\\Connection->runQueryCallback('alter table `at...', Array, Object(Closure))
#1 C:\\Users\\<USER>\\Desktop\\projects\\attendancetrackerv2A\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(557): Illuminate\\Database\\Connection->run('alter table `at...', Array, Object(Closure))
#2 C:\\Users\\<USER>\\Desktop\\projects\\attendancetrackerv2A\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Blueprint.php(119): Illuminate\\Database\\Connection->statement('alter table `at...')
#3 C:\\Users\\<USER>\\Desktop\\projects\\attendancetrackerv2A\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Builder.php(618): Illuminate\\Database\\Schema\\Blueprint->build()
#4 C:\\Users\\<USER>\\Desktop\\projects\\attendancetrackerv2A\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Builder.php(460): Illuminate\\Database\\Schema\\Builder->build(Object(Illuminate\\Database\\Schema\\Blueprint))
#5 C:\\Users\\<USER>\\Desktop\\projects\\attendancetrackerv2A\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(361): Illuminate\\Database\\Schema\\Builder->table('attendances', Object(Closure))
#6 C:\\Users\\<USER>\\Desktop\\projects\\attendancetrackerv2A\\database\\migrations\\2025_07_11_000000_add_unique_constraint_to_attendances.php(14): Illuminate\\Support\\Facades\\Facade::__callStatic('table', Array)
#7 C:\\Users\\<USER>\\Desktop\\projects\\attendancetrackerv2A\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(514): Illuminate\\Database\\Migrations\\Migration@anonymous->up()
#8 C:\\Users\\<USER>\\Desktop\\projects\\attendancetrackerv2A\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(439): Illuminate\\Database\\Migrations\\Migrator->runMethod(Object(Illuminate\\Database\\MySqlConnection), Object(Illuminate\\Database\\Migrations\\Migration@anonymous), 'up')
#9 C:\\Users\\<USER>\\Desktop\\projects\\attendancetrackerv2A\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(448): Illuminate\\Database\\Migrations\\Migrator->{closure:Illuminate\\Database\\Migrations\\Migrator::runMigration():435}()
#10 C:\\Users\\<USER>\\Desktop\\projects\\attendancetrackerv2A\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(250): Illuminate\\Database\\Migrations\\Migrator->runMigration(Object(Illuminate\\Database\\Migrations\\Migration@anonymous), 'up')
#11 C:\\Users\\<USER>\\Desktop\\projects\\attendancetrackerv2A\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\View\\Components\\Task.php(41): Illuminate\\Database\\Migrations\\Migrator->{closure:Illuminate\\Database\\Migrations\\Migrator::runUp():250}()
#12 C:\\Users\\<USER>\\Desktop\\projects\\attendancetrackerv2A\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(805): Illuminate\\Console\\View\\Components\\Task->render('2025_07_11_0000...', Object(Closure))
#13 C:\\Users\\<USER>\\Desktop\\projects\\attendancetrackerv2A\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(250): Illuminate\\Database\\Migrations\\Migrator->write('Illuminate\\\\Cons...', '2025_07_11_0000...', Object(Closure))
#14 C:\\Users\\<USER>\\Desktop\\projects\\attendancetrackerv2A\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(210): Illuminate\\Database\\Migrations\\Migrator->runUp('C:\\\\Users\\\\<USER>\\\\D...', 1, false)
#15 C:\\Users\\<USER>\\Desktop\\projects\\attendancetrackerv2A\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(137): Illuminate\\Database\\Migrations\\Migrator->runPending(Array, Array)
#16 C:\\Users\\<USER>\\Desktop\\projects\\attendancetrackerv2A\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(116): Illuminate\\Database\\Migrations\\Migrator->run(Array, Array)
#17 C:\\Users\\<USER>\\Desktop\\projects\\attendancetrackerv2A\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(665): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->{closure:Illuminate\\Database\\Console\\Migrations\\MigrateCommand::runMigrations():109}()
#18 C:\\Users\\<USER>\\Desktop\\projects\\attendancetrackerv2A\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(109): Illuminate\\Database\\Migrations\\Migrator->usingConnection(NULL, Object(Closure))
#19 C:\\Users\\<USER>\\Desktop\\projects\\attendancetrackerv2A\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(88): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->runMigrations()
#20 C:\\Users\\<USER>\\Desktop\\projects\\attendancetrackerv2A\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->handle()
#21 C:\\Users\\<USER>\\Desktop\\projects\\attendancetrackerv2A\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()
#22 C:\\Users\\<USER>\\Desktop\\projects\\attendancetrackerv2A\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#23 C:\\Users\\<USER>\\Desktop\\projects\\attendancetrackerv2A\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#24 C:\\Users\\<USER>\\Desktop\\projects\\attendancetrackerv2A\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(754): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#25 C:\\Users\\<USER>\\Desktop\\projects\\attendancetrackerv2A\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(211): Illuminate\\Container\\Container->call(Array)
#26 C:\\Users\\<USER>\\Desktop\\projects\\attendancetrackerv2A\\vendor\\symfony\\console\\Command\\Command.php(318): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArrayInput), Object(Illuminate\\Console\\OutputStyle))
#27 C:\\Users\\<USER>\\Desktop\\projects\\attendancetrackerv2A\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArrayInput), Object(Illuminate\\Console\\OutputStyle))
#28 C:\\Users\\<USER>\\Desktop\\projects\\attendancetrackerv2A\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Concerns\\CallsCommands.php(68): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArrayInput), Object(Illuminate\\Console\\OutputStyle))
#29 C:\\Users\\<USER>\\Desktop\\projects\\attendancetrackerv2A\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Concerns\\CallsCommands.php(29): Illuminate\\Console\\Command->runCommand('migrate', Array, Object(Illuminate\\Console\\OutputStyle))
#30 C:\\Users\\<USER>\\Desktop\\projects\\attendancetrackerv2A\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\FreshCommand.php(81): Illuminate\\Console\\Command->call('migrate', Array)
#31 C:\\Users\\<USER>\\Desktop\\projects\\attendancetrackerv2A\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Database\\Console\\Migrations\\FreshCommand->handle()
#32 C:\\Users\\<USER>\\Desktop\\projects\\attendancetrackerv2A\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()
#33 C:\\Users\\<USER>\\Desktop\\projects\\attendancetrackerv2A\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#34 C:\\Users\\<USER>\\Desktop\\projects\\attendancetrackerv2A\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#35 C:\\Users\\<USER>\\Desktop\\projects\\attendancetrackerv2A\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(754): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#36 C:\\Users\\<USER>\\Desktop\\projects\\attendancetrackerv2A\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(211): Illuminate\\Container\\Container->call(Array)
#37 C:\\Users\\<USER>\\Desktop\\projects\\attendancetrackerv2A\\vendor\\symfony\\console\\Command\\Command.php(318): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#38 C:\\Users\\<USER>\\Desktop\\projects\\attendancetrackerv2A\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#39 C:\\Users\\<USER>\\Desktop\\projects\\attendancetrackerv2A\\vendor\\symfony\\console\\Application.php(1092): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#40 C:\\Users\\<USER>\\Desktop\\projects\\attendancetrackerv2A\\vendor\\symfony\\console\\Application.php(341): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Database\\Console\\Migrations\\FreshCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#41 C:\\Users\\<USER>\\Desktop\\projects\\attendancetrackerv2A\\vendor\\symfony\\console\\Application.php(192): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#42 C:\\Users\\<USER>\\Desktop\\projects\\attendancetrackerv2A\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(197): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#43 C:\\Users\\<USER>\\Desktop\\projects\\attendancetrackerv2A\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1234): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#44 C:\\Users\\<USER>\\Desktop\\projects\\attendancetrackerv2A\\artisan(16): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#45 {main}

[previous exception] [object] (PDOException(code: 42000): SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near 'check_in_time))' at line 1 at C:\\Users\\<USER>\\Desktop\\projects\\attendancetrackerv2A\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:562)
[stacktrace]
#0 C:\\Users\\<USER>\\Desktop\\projects\\attendancetrackerv2A\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(562): PDO->prepare('alter table `at...')
#1 C:\\Users\\<USER>\\Desktop\\projects\\attendancetrackerv2A\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(809): Illuminate\\Database\\Connection->{closure:Illuminate\\Database\\Connection::statement():557}('alter table `at...', Array)
#2 C:\\Users\\<USER>\\Desktop\\projects\\attendancetrackerv2A\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(776): Illuminate\\Database\\Connection->runQueryCallback('alter table `at...', Array, Object(Closure))
#3 C:\\Users\\<USER>\\Desktop\\projects\\attendancetrackerv2A\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(557): Illuminate\\Database\\Connection->run('alter table `at...', Array, Object(Closure))
#4 C:\\Users\\<USER>\\Desktop\\projects\\attendancetrackerv2A\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Blueprint.php(119): Illuminate\\Database\\Connection->statement('alter table `at...')
#5 C:\\Users\\<USER>\\Desktop\\projects\\attendancetrackerv2A\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Builder.php(618): Illuminate\\Database\\Schema\\Blueprint->build()
#6 C:\\Users\\<USER>\\Desktop\\projects\\attendancetrackerv2A\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Builder.php(460): Illuminate\\Database\\Schema\\Builder->build(Object(Illuminate\\Database\\Schema\\Blueprint))
#7 C:\\Users\\<USER>\\Desktop\\projects\\attendancetrackerv2A\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(361): Illuminate\\Database\\Schema\\Builder->table('attendances', Object(Closure))
#8 C:\\Users\\<USER>\\Desktop\\projects\\attendancetrackerv2A\\database\\migrations\\2025_07_11_000000_add_unique_constraint_to_attendances.php(14): Illuminate\\Support\\Facades\\Facade::__callStatic('table', Array)
#9 C:\\Users\\<USER>\\Desktop\\projects\\attendancetrackerv2A\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(514): Illuminate\\Database\\Migrations\\Migration@anonymous->up()
#10 C:\\Users\\<USER>\\Desktop\\projects\\attendancetrackerv2A\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(439): Illuminate\\Database\\Migrations\\Migrator->runMethod(Object(Illuminate\\Database\\MySqlConnection), Object(Illuminate\\Database\\Migrations\\Migration@anonymous), 'up')
#11 C:\\Users\\<USER>\\Desktop\\projects\\attendancetrackerv2A\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(448): Illuminate\\Database\\Migrations\\Migrator->{closure:Illuminate\\Database\\Migrations\\Migrator::runMigration():435}()
#12 C:\\Users\\<USER>\\Desktop\\projects\\attendancetrackerv2A\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(250): Illuminate\\Database\\Migrations\\Migrator->runMigration(Object(Illuminate\\Database\\Migrations\\Migration@anonymous), 'up')
#13 C:\\Users\\<USER>\\Desktop\\projects\\attendancetrackerv2A\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\View\\Components\\Task.php(41): Illuminate\\Database\\Migrations\\Migrator->{closure:Illuminate\\Database\\Migrations\\Migrator::runUp():250}()
#14 C:\\Users\\<USER>\\Desktop\\projects\\attendancetrackerv2A\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(805): Illuminate\\Console\\View\\Components\\Task->render('2025_07_11_0000...', Object(Closure))
#15 C:\\Users\\<USER>\\Desktop\\projects\\attendancetrackerv2A\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(250): Illuminate\\Database\\Migrations\\Migrator->write('Illuminate\\\\Cons...', '2025_07_11_0000...', Object(Closure))
#16 C:\\Users\\<USER>\\Desktop\\projects\\attendancetrackerv2A\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(210): Illuminate\\Database\\Migrations\\Migrator->runUp('C:\\\\Users\\\\<USER>\\\\D...', 1, false)
#17 C:\\Users\\<USER>\\Desktop\\projects\\attendancetrackerv2A\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(137): Illuminate\\Database\\Migrations\\Migrator->runPending(Array, Array)
#18 C:\\Users\\<USER>\\Desktop\\projects\\attendancetrackerv2A\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(116): Illuminate\\Database\\Migrations\\Migrator->run(Array, Array)
#19 C:\\Users\\<USER>\\Desktop\\projects\\attendancetrackerv2A\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(665): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->{closure:Illuminate\\Database\\Console\\Migrations\\MigrateCommand::runMigrations():109}()
#20 C:\\Users\\<USER>\\Desktop\\projects\\attendancetrackerv2A\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(109): Illuminate\\Database\\Migrations\\Migrator->usingConnection(NULL, Object(Closure))
#21 C:\\Users\\<USER>\\Desktop\\projects\\attendancetrackerv2A\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(88): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->runMigrations()
#22 C:\\Users\\<USER>\\Desktop\\projects\\attendancetrackerv2A\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->handle()
#23 C:\\Users\\<USER>\\Desktop\\projects\\attendancetrackerv2A\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()
#24 C:\\Users\\<USER>\\Desktop\\projects\\attendancetrackerv2A\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#25 C:\\Users\\<USER>\\Desktop\\projects\\attendancetrackerv2A\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#26 C:\\Users\\<USER>\\Desktop\\projects\\attendancetrackerv2A\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(754): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#27 C:\\Users\\<USER>\\Desktop\\projects\\attendancetrackerv2A\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(211): Illuminate\\Container\\Container->call(Array)
#28 C:\\Users\\<USER>\\Desktop\\projects\\attendancetrackerv2A\\vendor\\symfony\\console\\Command\\Command.php(318): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArrayInput), Object(Illuminate\\Console\\OutputStyle))
#29 C:\\Users\\<USER>\\Desktop\\projects\\attendancetrackerv2A\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArrayInput), Object(Illuminate\\Console\\OutputStyle))
#30 C:\\Users\\<USER>\\Desktop\\projects\\attendancetrackerv2A\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Concerns\\CallsCommands.php(68): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArrayInput), Object(Illuminate\\Console\\OutputStyle))
#31 C:\\Users\\<USER>\\Desktop\\projects\\attendancetrackerv2A\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Concerns\\CallsCommands.php(29): Illuminate\\Console\\Command->runCommand('migrate', Array, Object(Illuminate\\Console\\OutputStyle))
#32 C:\\Users\\<USER>\\Desktop\\projects\\attendancetrackerv2A\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\FreshCommand.php(81): Illuminate\\Console\\Command->call('migrate', Array)
#33 C:\\Users\\<USER>\\Desktop\\projects\\attendancetrackerv2A\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Database\\Console\\Migrations\\FreshCommand->handle()
#34 C:\\Users\\<USER>\\Desktop\\projects\\attendancetrackerv2A\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()
#35 C:\\Users\\<USER>\\Desktop\\projects\\attendancetrackerv2A\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#36 C:\\Users\\<USER>\\Desktop\\projects\\attendancetrackerv2A\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#37 C:\\Users\\<USER>\\Desktop\\projects\\attendancetrackerv2A\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(754): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#38 C:\\Users\\<USER>\\Desktop\\projects\\attendancetrackerv2A\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(211): Illuminate\\Container\\Container->call(Array)
#39 C:\\Users\\<USER>\\Desktop\\projects\\attendancetrackerv2A\\vendor\\symfony\\console\\Command\\Command.php(318): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#40 C:\\Users\\<USER>\\Desktop\\projects\\attendancetrackerv2A\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#41 C:\\Users\\<USER>\\Desktop\\projects\\attendancetrackerv2A\\vendor\\symfony\\console\\Application.php(1092): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#42 C:\\Users\\<USER>\\Desktop\\projects\\attendancetrackerv2A\\vendor\\symfony\\console\\Application.php(341): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Database\\Console\\Migrations\\FreshCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#43 C:\\Users\\<USER>\\Desktop\\projects\\attendancetrackerv2A\\vendor\\symfony\\console\\Application.php(192): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#44 C:\\Users\\<USER>\\Desktop\\projects\\attendancetrackerv2A\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(197): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#45 C:\\Users\\<USER>\\Desktop\\projects\\attendancetrackerv2A\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1234): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#46 C:\\Users\\<USER>\\Desktop\\projects\\attendancetrackerv2A\\artisan(16): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#47 {main}
"} 
[2025-07-12 00:41:33] local.ERROR: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near 'check_in_time))' at line 1 (Connection: mysql, SQL: alter table `attendances` add unique `unique_employee_daily_attendance`(`employee_profile_id`, DATE(check_in_time))) {"exception":"[object] (Illuminate\\Database\\QueryException(code: 42000): SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near 'check_in_time))' at line 1 (Connection: mysql, SQL: alter table `attendances` add unique `unique_employee_daily_attendance`(`employee_profile_id`, DATE(check_in_time))) at C:\\Users\\<USER>\\Desktop\\projects\\attendancetrackerv2A\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:822)
[stacktrace]
#0 C:\\Users\\<USER>\\Desktop\\projects\\attendancetrackerv2A\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(776): Illuminate\\Database\\Connection->runQueryCallback('alter table `at...', Array, Object(Closure))
#1 C:\\Users\\<USER>\\Desktop\\projects\\attendancetrackerv2A\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(557): Illuminate\\Database\\Connection->run('alter table `at...', Array, Object(Closure))
#2 C:\\Users\\<USER>\\Desktop\\projects\\attendancetrackerv2A\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Blueprint.php(119): Illuminate\\Database\\Connection->statement('alter table `at...')
#3 C:\\Users\\<USER>\\Desktop\\projects\\attendancetrackerv2A\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Builder.php(618): Illuminate\\Database\\Schema\\Blueprint->build()
#4 C:\\Users\\<USER>\\Desktop\\projects\\attendancetrackerv2A\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Builder.php(460): Illuminate\\Database\\Schema\\Builder->build(Object(Illuminate\\Database\\Schema\\Blueprint))
#5 C:\\Users\\<USER>\\Desktop\\projects\\attendancetrackerv2A\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(361): Illuminate\\Database\\Schema\\Builder->table('attendances', Object(Closure))
#6 C:\\Users\\<USER>\\Desktop\\projects\\attendancetrackerv2A\\database\\migrations\\2025_07_11_000000_add_unique_constraint_to_attendances.php(14): Illuminate\\Support\\Facades\\Facade::__callStatic('table', Array)
#7 C:\\Users\\<USER>\\Desktop\\projects\\attendancetrackerv2A\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(514): Illuminate\\Database\\Migrations\\Migration@anonymous->up()
#8 C:\\Users\\<USER>\\Desktop\\projects\\attendancetrackerv2A\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(439): Illuminate\\Database\\Migrations\\Migrator->runMethod(Object(Illuminate\\Database\\MySqlConnection), Object(Illuminate\\Database\\Migrations\\Migration@anonymous), 'up')
#9 C:\\Users\\<USER>\\Desktop\\projects\\attendancetrackerv2A\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(448): Illuminate\\Database\\Migrations\\Migrator->{closure:Illuminate\\Database\\Migrations\\Migrator::runMigration():435}()
#10 C:\\Users\\<USER>\\Desktop\\projects\\attendancetrackerv2A\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(250): Illuminate\\Database\\Migrations\\Migrator->runMigration(Object(Illuminate\\Database\\Migrations\\Migration@anonymous), 'up')
#11 C:\\Users\\<USER>\\Desktop\\projects\\attendancetrackerv2A\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\View\\Components\\Task.php(41): Illuminate\\Database\\Migrations\\Migrator->{closure:Illuminate\\Database\\Migrations\\Migrator::runUp():250}()
#12 C:\\Users\\<USER>\\Desktop\\projects\\attendancetrackerv2A\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(805): Illuminate\\Console\\View\\Components\\Task->render('2025_07_11_0000...', Object(Closure))
#13 C:\\Users\\<USER>\\Desktop\\projects\\attendancetrackerv2A\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(250): Illuminate\\Database\\Migrations\\Migrator->write('Illuminate\\\\Cons...', '2025_07_11_0000...', Object(Closure))
#14 C:\\Users\\<USER>\\Desktop\\projects\\attendancetrackerv2A\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(210): Illuminate\\Database\\Migrations\\Migrator->runUp('C:\\\\Users\\\\<USER>\\\\D...', 1, false)
#15 C:\\Users\\<USER>\\Desktop\\projects\\attendancetrackerv2A\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(137): Illuminate\\Database\\Migrations\\Migrator->runPending(Array, Array)
#16 C:\\Users\\<USER>\\Desktop\\projects\\attendancetrackerv2A\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(116): Illuminate\\Database\\Migrations\\Migrator->run(Array, Array)
#17 C:\\Users\\<USER>\\Desktop\\projects\\attendancetrackerv2A\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(665): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->{closure:Illuminate\\Database\\Console\\Migrations\\MigrateCommand::runMigrations():109}()
#18 C:\\Users\\<USER>\\Desktop\\projects\\attendancetrackerv2A\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(109): Illuminate\\Database\\Migrations\\Migrator->usingConnection(NULL, Object(Closure))
#19 C:\\Users\\<USER>\\Desktop\\projects\\attendancetrackerv2A\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(88): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->runMigrations()
#20 C:\\Users\\<USER>\\Desktop\\projects\\attendancetrackerv2A\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->handle()
#21 C:\\Users\\<USER>\\Desktop\\projects\\attendancetrackerv2A\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()
#22 C:\\Users\\<USER>\\Desktop\\projects\\attendancetrackerv2A\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#23 C:\\Users\\<USER>\\Desktop\\projects\\attendancetrackerv2A\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#24 C:\\Users\\<USER>\\Desktop\\projects\\attendancetrackerv2A\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(754): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#25 C:\\Users\\<USER>\\Desktop\\projects\\attendancetrackerv2A\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(211): Illuminate\\Container\\Container->call(Array)
#26 C:\\Users\\<USER>\\Desktop\\projects\\attendancetrackerv2A\\vendor\\symfony\\console\\Command\\Command.php(318): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArrayInput), Object(Illuminate\\Console\\OutputStyle))
#27 C:\\Users\\<USER>\\Desktop\\projects\\attendancetrackerv2A\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArrayInput), Object(Illuminate\\Console\\OutputStyle))
#28 C:\\Users\\<USER>\\Desktop\\projects\\attendancetrackerv2A\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Concerns\\CallsCommands.php(68): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArrayInput), Object(Illuminate\\Console\\OutputStyle))
#29 C:\\Users\\<USER>\\Desktop\\projects\\attendancetrackerv2A\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Concerns\\CallsCommands.php(29): Illuminate\\Console\\Command->runCommand('migrate', Array, Object(Illuminate\\Console\\OutputStyle))
#30 C:\\Users\\<USER>\\Desktop\\projects\\attendancetrackerv2A\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\FreshCommand.php(81): Illuminate\\Console\\Command->call('migrate', Array)
#31 C:\\Users\\<USER>\\Desktop\\projects\\attendancetrackerv2A\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Database\\Console\\Migrations\\FreshCommand->handle()
#32 C:\\Users\\<USER>\\Desktop\\projects\\attendancetrackerv2A\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()
#33 C:\\Users\\<USER>\\Desktop\\projects\\attendancetrackerv2A\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#34 C:\\Users\\<USER>\\Desktop\\projects\\attendancetrackerv2A\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#35 C:\\Users\\<USER>\\Desktop\\projects\\attendancetrackerv2A\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(754): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#36 C:\\Users\\<USER>\\Desktop\\projects\\attendancetrackerv2A\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(211): Illuminate\\Container\\Container->call(Array)
#37 C:\\Users\\<USER>\\Desktop\\projects\\attendancetrackerv2A\\vendor\\symfony\\console\\Command\\Command.php(318): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#38 C:\\Users\\<USER>\\Desktop\\projects\\attendancetrackerv2A\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#39 C:\\Users\\<USER>\\Desktop\\projects\\attendancetrackerv2A\\vendor\\symfony\\console\\Application.php(1092): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#40 C:\\Users\\<USER>\\Desktop\\projects\\attendancetrackerv2A\\vendor\\symfony\\console\\Application.php(341): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Database\\Console\\Migrations\\FreshCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#41 C:\\Users\\<USER>\\Desktop\\projects\\attendancetrackerv2A\\vendor\\symfony\\console\\Application.php(192): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#42 C:\\Users\\<USER>\\Desktop\\projects\\attendancetrackerv2A\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(197): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#43 C:\\Users\\<USER>\\Desktop\\projects\\attendancetrackerv2A\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1234): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#44 C:\\Users\\<USER>\\Desktop\\projects\\attendancetrackerv2A\\artisan(16): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#45 {main}

[previous exception] [object] (PDOException(code: 42000): SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near 'check_in_time))' at line 1 at C:\\Users\\<USER>\\Desktop\\projects\\attendancetrackerv2A\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:562)
[stacktrace]
#0 C:\\Users\\<USER>\\Desktop\\projects\\attendancetrackerv2A\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(562): PDO->prepare('alter table `at...')
#1 C:\\Users\\<USER>\\Desktop\\projects\\attendancetrackerv2A\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(809): Illuminate\\Database\\Connection->{closure:Illuminate\\Database\\Connection::statement():557}('alter table `at...', Array)
#2 C:\\Users\\<USER>\\Desktop\\projects\\attendancetrackerv2A\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(776): Illuminate\\Database\\Connection->runQueryCallback('alter table `at...', Array, Object(Closure))
#3 C:\\Users\\<USER>\\Desktop\\projects\\attendancetrackerv2A\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(557): Illuminate\\Database\\Connection->run('alter table `at...', Array, Object(Closure))
#4 C:\\Users\\<USER>\\Desktop\\projects\\attendancetrackerv2A\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Blueprint.php(119): Illuminate\\Database\\Connection->statement('alter table `at...')
#5 C:\\Users\\<USER>\\Desktop\\projects\\attendancetrackerv2A\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Builder.php(618): Illuminate\\Database\\Schema\\Blueprint->build()
#6 C:\\Users\\<USER>\\Desktop\\projects\\attendancetrackerv2A\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Builder.php(460): Illuminate\\Database\\Schema\\Builder->build(Object(Illuminate\\Database\\Schema\\Blueprint))
#7 C:\\Users\\<USER>\\Desktop\\projects\\attendancetrackerv2A\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(361): Illuminate\\Database\\Schema\\Builder->table('attendances', Object(Closure))
#8 C:\\Users\\<USER>\\Desktop\\projects\\attendancetrackerv2A\\database\\migrations\\2025_07_11_000000_add_unique_constraint_to_attendances.php(14): Illuminate\\Support\\Facades\\Facade::__callStatic('table', Array)
#9 C:\\Users\\<USER>\\Desktop\\projects\\attendancetrackerv2A\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(514): Illuminate\\Database\\Migrations\\Migration@anonymous->up()
#10 C:\\Users\\<USER>\\Desktop\\projects\\attendancetrackerv2A\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(439): Illuminate\\Database\\Migrations\\Migrator->runMethod(Object(Illuminate\\Database\\MySqlConnection), Object(Illuminate\\Database\\Migrations\\Migration@anonymous), 'up')
#11 C:\\Users\\<USER>\\Desktop\\projects\\attendancetrackerv2A\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(448): Illuminate\\Database\\Migrations\\Migrator->{closure:Illuminate\\Database\\Migrations\\Migrator::runMigration():435}()
#12 C:\\Users\\<USER>\\Desktop\\projects\\attendancetrackerv2A\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(250): Illuminate\\Database\\Migrations\\Migrator->runMigration(Object(Illuminate\\Database\\Migrations\\Migration@anonymous), 'up')
#13 C:\\Users\\<USER>\\Desktop\\projects\\attendancetrackerv2A\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\View\\Components\\Task.php(41): Illuminate\\Database\\Migrations\\Migrator->{closure:Illuminate\\Database\\Migrations\\Migrator::runUp():250}()
#14 C:\\Users\\<USER>\\Desktop\\projects\\attendancetrackerv2A\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(805): Illuminate\\Console\\View\\Components\\Task->render('2025_07_11_0000...', Object(Closure))
#15 C:\\Users\\<USER>\\Desktop\\projects\\attendancetrackerv2A\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(250): Illuminate\\Database\\Migrations\\Migrator->write('Illuminate\\\\Cons...', '2025_07_11_0000...', Object(Closure))
#16 C:\\Users\\<USER>\\Desktop\\projects\\attendancetrackerv2A\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(210): Illuminate\\Database\\Migrations\\Migrator->runUp('C:\\\\Users\\\\<USER>\\\\D...', 1, false)
#17 C:\\Users\\<USER>\\Desktop\\projects\\attendancetrackerv2A\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(137): Illuminate\\Database\\Migrations\\Migrator->runPending(Array, Array)
#18 C:\\Users\\<USER>\\Desktop\\projects\\attendancetrackerv2A\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(116): Illuminate\\Database\\Migrations\\Migrator->run(Array, Array)
#19 C:\\Users\\<USER>\\Desktop\\projects\\attendancetrackerv2A\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(665): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->{closure:Illuminate\\Database\\Console\\Migrations\\MigrateCommand::runMigrations():109}()
#20 C:\\Users\\<USER>\\Desktop\\projects\\attendancetrackerv2A\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(109): Illuminate\\Database\\Migrations\\Migrator->usingConnection(NULL, Object(Closure))
#21 C:\\Users\\<USER>\\Desktop\\projects\\attendancetrackerv2A\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(88): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->runMigrations()
#22 C:\\Users\\<USER>\\Desktop\\projects\\attendancetrackerv2A\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->handle()
#23 C:\\Users\\<USER>\\Desktop\\projects\\attendancetrackerv2A\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()
#24 C:\\Users\\<USER>\\Desktop\\projects\\attendancetrackerv2A\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#25 C:\\Users\\<USER>\\Desktop\\projects\\attendancetrackerv2A\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#26 C:\\Users\\<USER>\\Desktop\\projects\\attendancetrackerv2A\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(754): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#27 C:\\Users\\<USER>\\Desktop\\projects\\attendancetrackerv2A\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(211): Illuminate\\Container\\Container->call(Array)
#28 C:\\Users\\<USER>\\Desktop\\projects\\attendancetrackerv2A\\vendor\\symfony\\console\\Command\\Command.php(318): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArrayInput), Object(Illuminate\\Console\\OutputStyle))
#29 C:\\Users\\<USER>\\Desktop\\projects\\attendancetrackerv2A\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArrayInput), Object(Illuminate\\Console\\OutputStyle))
#30 C:\\Users\\<USER>\\Desktop\\projects\\attendancetrackerv2A\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Concerns\\CallsCommands.php(68): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArrayInput), Object(Illuminate\\Console\\OutputStyle))
#31 C:\\Users\\<USER>\\Desktop\\projects\\attendancetrackerv2A\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Concerns\\CallsCommands.php(29): Illuminate\\Console\\Command->runCommand('migrate', Array, Object(Illuminate\\Console\\OutputStyle))
#32 C:\\Users\\<USER>\\Desktop\\projects\\attendancetrackerv2A\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\FreshCommand.php(81): Illuminate\\Console\\Command->call('migrate', Array)
#33 C:\\Users\\<USER>\\Desktop\\projects\\attendancetrackerv2A\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Database\\Console\\Migrations\\FreshCommand->handle()
#34 C:\\Users\\<USER>\\Desktop\\projects\\attendancetrackerv2A\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()
#35 C:\\Users\\<USER>\\Desktop\\projects\\attendancetrackerv2A\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#36 C:\\Users\\<USER>\\Desktop\\projects\\attendancetrackerv2A\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#37 C:\\Users\\<USER>\\Desktop\\projects\\attendancetrackerv2A\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(754): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#38 C:\\Users\\<USER>\\Desktop\\projects\\attendancetrackerv2A\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(211): Illuminate\\Container\\Container->call(Array)
#39 C:\\Users\\<USER>\\Desktop\\projects\\attendancetrackerv2A\\vendor\\symfony\\console\\Command\\Command.php(318): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#40 C:\\Users\\<USER>\\Desktop\\projects\\attendancetrackerv2A\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#41 C:\\Users\\<USER>\\Desktop\\projects\\attendancetrackerv2A\\vendor\\symfony\\console\\Application.php(1092): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#42 C:\\Users\\<USER>\\Desktop\\projects\\attendancetrackerv2A\\vendor\\symfony\\console\\Application.php(341): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Database\\Console\\Migrations\\FreshCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#43 C:\\Users\\<USER>\\Desktop\\projects\\attendancetrackerv2A\\vendor\\symfony\\console\\Application.php(192): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#44 C:\\Users\\<USER>\\Desktop\\projects\\attendancetrackerv2A\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(197): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#45 C:\\Users\\<USER>\\Desktop\\projects\\attendancetrackerv2A\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1234): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#46 C:\\Users\\<USER>\\Desktop\\projects\\attendancetrackerv2A\\artisan(16): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#47 {main}
"} 
[2025-07-12 00:44:32] local.INFO: === REQUEST PARAMETERS DEBUG === {"all_request_params":[],"weekly_start_date_param":null,"weekly_end_date_param":null,"year_param":null,"month_param":null,"calculated_weekly_start":"2025-07-06","calculated_weekly_end":"2025-07-12"} 
[2025-07-12 00:45:40] local.INFO: === REQUEST PARAMETERS DEBUG === {"all_request_params":[],"weekly_start_date_param":null,"weekly_end_date_param":null,"year_param":null,"month_param":null,"calculated_weekly_start":"2025-07-06","calculated_weekly_end":"2025-07-12"} 
[2025-07-12 00:46:05] local.INFO: === REQUEST PARAMETERS DEBUG === {"all_request_params":{"month":"6","year":"2025"},"weekly_start_date_param":null,"weekly_end_date_param":null,"year_param":"2025","month_param":"6","calculated_weekly_start":"2025-07-06","calculated_weekly_end":"2025-07-12"} 
[2025-07-12 00:46:08] local.INFO: === REQUEST PARAMETERS DEBUG === {"all_request_params":{"month":"7","year":"2025"},"weekly_start_date_param":null,"weekly_end_date_param":null,"year_param":"2025","month_param":"7","calculated_weekly_start":"2025-07-06","calculated_weekly_end":"2025-07-12"} 
[2025-07-12 00:46:22] local.INFO: === REQUEST PARAMETERS DEBUG === {"all_request_params":{"weekly_start_date":"2025-06-29","weekly_end_date":"2025-07-05"},"weekly_start_date_param":"2025-06-29","weekly_end_date_param":"2025-07-05","year_param":null,"month_param":null,"calculated_weekly_start":"2025-06-29","calculated_weekly_end":"2025-07-05"} 
[2025-07-12 00:46:43] local.INFO: === REQUEST PARAMETERS DEBUG === {"all_request_params":{"weekly_start_date":"2025-06-22","weekly_end_date":"2025-06-28"},"weekly_start_date_param":"2025-06-22","weekly_end_date_param":"2025-06-28","year_param":null,"month_param":null,"calculated_weekly_start":"2025-06-22","calculated_weekly_end":"2025-06-28"} 
[2025-07-12 00:46:56] local.INFO: === REQUEST PARAMETERS DEBUG === {"all_request_params":{"weekly_start_date":"2025-06-15","weekly_end_date":"2025-06-21"},"weekly_start_date_param":"2025-06-15","weekly_end_date_param":"2025-06-21","year_param":null,"month_param":null,"calculated_weekly_start":"2025-06-15","calculated_weekly_end":"2025-06-21"} 
[2025-07-12 00:47:17] local.INFO: === REQUEST PARAMETERS DEBUG === {"all_request_params":{"weekly_start_date":"2025-07-01","weekly_end_date":"2025-07-07"},"weekly_start_date_param":"2025-07-01","weekly_end_date_param":"2025-07-07","year_param":null,"month_param":null,"calculated_weekly_start":"2025-07-01","calculated_weekly_end":"2025-07-07"} 
[2025-07-12 00:47:27] local.INFO: === REQUEST PARAMETERS DEBUG === {"all_request_params":{"weekly_start_date":"2025-07-01","weekly_end_date":"2025-07-07"},"weekly_start_date_param":"2025-07-01","weekly_end_date_param":"2025-07-07","year_param":null,"month_param":null,"calculated_weekly_start":"2025-07-01","calculated_weekly_end":"2025-07-07"} 
[2025-07-12 01:01:49] local.INFO: === REQUEST PARAMETERS DEBUG === {"all_request_params":[],"weekly_start_date_param":null,"weekly_end_date_param":null,"year_param":null,"month_param":null,"calculated_weekly_start":"2025-07-06","calculated_weekly_end":"2025-07-12"} 
[2025-07-12 01:02:04] local.INFO: === REQUEST PARAMETERS DEBUG === {"all_request_params":{"month":"6","year":"2025"},"weekly_start_date_param":null,"weekly_end_date_param":null,"year_param":"2025","month_param":"6","calculated_weekly_start":"2025-07-06","calculated_weekly_end":"2025-07-12"} 
[2025-07-12 01:02:05] local.INFO: === REQUEST PARAMETERS DEBUG === {"all_request_params":{"month":"7","year":"2025"},"weekly_start_date_param":null,"weekly_end_date_param":null,"year_param":"2025","month_param":"7","calculated_weekly_start":"2025-07-06","calculated_weekly_end":"2025-07-12"} 
[2025-07-12 01:02:06] local.INFO: === REQUEST PARAMETERS DEBUG === {"all_request_params":{"month":"8","year":"2025"},"weekly_start_date_param":null,"weekly_end_date_param":null,"year_param":"2025","month_param":"8","calculated_weekly_start":"2025-07-06","calculated_weekly_end":"2025-07-12"} 
[2025-07-12 01:02:09] local.INFO: === REQUEST PARAMETERS DEBUG === {"all_request_params":{"month":"7","year":"2025"},"weekly_start_date_param":null,"weekly_end_date_param":null,"year_param":"2025","month_param":"7","calculated_weekly_start":"2025-07-06","calculated_weekly_end":"2025-07-12"} 
[2025-07-12 01:02:35] local.INFO: === REQUEST PARAMETERS DEBUG === {"all_request_params":{"weekly_start_date":"2025-06-29","weekly_end_date":"2025-07-05"},"weekly_start_date_param":"2025-06-29","weekly_end_date_param":"2025-07-05","year_param":null,"month_param":null,"calculated_weekly_start":"2025-06-29","calculated_weekly_end":"2025-07-05"} 
[2025-07-12 01:02:49] local.INFO: === REQUEST PARAMETERS DEBUG === {"all_request_params":{"weekly_start_date":"2025-06-22","weekly_end_date":"2025-06-28"},"weekly_start_date_param":"2025-06-22","weekly_end_date_param":"2025-06-28","year_param":null,"month_param":null,"calculated_weekly_start":"2025-06-22","calculated_weekly_end":"2025-06-28"} 
[2025-07-12 01:06:40] local.INFO: === REQUEST PARAMETERS DEBUG === {"all_request_params":[],"weekly_start_date_param":null,"weekly_end_date_param":null,"year_param":null,"month_param":null,"calculated_weekly_start":"2025-07-06","calculated_weekly_end":"2025-07-12"} 
[2025-07-12 01:07:04] local.INFO: === REQUEST PARAMETERS DEBUG === {"all_request_params":{"weekly_start_date":"2025-07-02","weekly_end_date":"2025-07-08"},"weekly_start_date_param":"2025-07-02","weekly_end_date_param":"2025-07-08","year_param":null,"month_param":null,"calculated_weekly_start":"2025-07-02","calculated_weekly_end":"2025-07-08"} 
[2025-07-12 01:07:21] local.INFO: === REQUEST PARAMETERS DEBUG === {"all_request_params":{"weekly_start_date":"2025-06-22","weekly_end_date":"2025-06-28"},"weekly_start_date_param":"2025-06-22","weekly_end_date_param":"2025-06-28","year_param":null,"month_param":null,"calculated_weekly_start":"2025-06-22","calculated_weekly_end":"2025-06-28"} 
[2025-07-12 01:07:41] local.INFO: === REQUEST PARAMETERS DEBUG === {"all_request_params":{"weekly_start_date":"2025-06-08","weekly_end_date":"2025-06-14"},"weekly_start_date_param":"2025-06-08","weekly_end_date_param":"2025-06-14","year_param":null,"month_param":null,"calculated_weekly_start":"2025-06-08","calculated_weekly_end":"2025-06-14"} 
[2025-07-12 01:15:44] local.INFO: === REQUEST PARAMETERS DEBUG === {"all_request_params":{"weekly_start_date":"2025-06-01","weekly_end_date":"2025-06-07"},"weekly_start_date_param":"2025-06-01","weekly_end_date_param":"2025-06-07","year_param":null,"month_param":null,"calculated_weekly_start":"2025-06-01","calculated_weekly_end":"2025-06-07"} 
[2025-07-12 01:23:38] local.INFO: === REQUEST PARAMETERS DEBUG === {"all_request_params":[],"weekly_start_date_param":null,"weekly_end_date_param":null,"year_param":null,"month_param":null,"calculated_weekly_start":"2025-07-06","calculated_weekly_end":"2025-07-12"} 
[2025-07-12 01:26:26] local.ERROR: The "--table" option does not exist. {"exception":"[object] (Symfony\\Component\\Console\\Exception\\RuntimeException(code: 0): The \"--table\" option does not exist. at C:\\Users\\<USER>\\Desktop\\projects\\attendancetrackerv2A\\vendor\\symfony\\console\\Input\\ArgvInput.php:226)
[stacktrace]
#0 C:\\Users\\<USER>\\Desktop\\projects\\attendancetrackerv2A\\vendor\\symfony\\console\\Input\\ArgvInput.php(153): Symfony\\Component\\Console\\Input\\ArgvInput->addLongOption('table', 'users')
#1 C:\\Users\\<USER>\\Desktop\\projects\\attendancetrackerv2A\\vendor\\symfony\\console\\Input\\ArgvInput.php(88): Symfony\\Component\\Console\\Input\\ArgvInput->parseLongOption('--table=users')
#2 C:\\Users\\<USER>\\Desktop\\projects\\attendancetrackerv2A\\vendor\\symfony\\console\\Input\\ArgvInput.php(77): Symfony\\Component\\Console\\Input\\ArgvInput->parseToken('--table=users', true)
#3 C:\\Users\\<USER>\\Desktop\\projects\\attendancetrackerv2A\\vendor\\symfony\\console\\Input\\Input.php(53): Symfony\\Component\\Console\\Input\\ArgvInput->parse()
#4 C:\\Users\\<USER>\\Desktop\\projects\\attendancetrackerv2A\\vendor\\symfony\\console\\Command\\Command.php(276): Symfony\\Component\\Console\\Input\\Input->bind(Object(Symfony\\Component\\Console\\Input\\InputDefinition))
#5 C:\\Users\\<USER>\\Desktop\\projects\\attendancetrackerv2A\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#6 C:\\Users\\<USER>\\Desktop\\projects\\attendancetrackerv2A\\vendor\\symfony\\console\\Application.php(1092): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#7 C:\\Users\\<USER>\\Desktop\\projects\\attendancetrackerv2A\\vendor\\symfony\\console\\Application.php(341): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Database\\Console\\ShowCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#8 C:\\Users\\<USER>\\Desktop\\projects\\attendancetrackerv2A\\vendor\\symfony\\console\\Application.php(192): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#9 C:\\Users\\<USER>\\Desktop\\projects\\attendancetrackerv2A\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(197): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#10 C:\\Users\\<USER>\\Desktop\\projects\\attendancetrackerv2A\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1234): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#11 C:\\Users\\<USER>\\Desktop\\projects\\attendancetrackerv2A\\artisan(16): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#12 {main}
"} 
[2025-07-12 01:26:32] local.INFO: === REQUEST PARAMETERS DEBUG === {"all_request_params":[],"weekly_start_date_param":null,"weekly_end_date_param":null,"year_param":null,"month_param":null,"calculated_weekly_start":"2025-07-06","calculated_weekly_end":"2025-07-12"} 
[2025-07-12 01:27:17] local.INFO: === REQUEST PARAMETERS DEBUG === {"all_request_params":[],"weekly_start_date_param":null,"weekly_end_date_param":null,"year_param":null,"month_param":null,"calculated_weekly_start":"2025-07-06","calculated_weekly_end":"2025-07-12"} 
[2025-07-12 01:29:13] local.INFO: === REQUEST PARAMETERS DEBUG === {"all_request_params":[],"weekly_start_date_param":null,"weekly_end_date_param":null,"year_param":null,"month_param":null,"calculated_weekly_start":"2025-07-06","calculated_weekly_end":"2025-07-12"} 
[2025-07-12 01:32:18] local.INFO: === REQUEST PARAMETERS DEBUG === {"all_request_params":[],"weekly_start_date_param":null,"weekly_end_date_param":null,"year_param":null,"month_param":null,"calculated_weekly_start":"2025-07-06","calculated_weekly_end":"2025-07-12"} 
[2025-07-12 09:56:42] local.INFO: === REQUEST PARAMETERS DEBUG === {"all_request_params":[],"weekly_start_date_param":null,"weekly_end_date_param":null,"year_param":null,"month_param":null,"calculated_weekly_start":"2025-07-06","calculated_weekly_end":"2025-07-12"} 
[2025-07-12 10:48:52] local.INFO: === REQUEST PARAMETERS DEBUG === {"all_request_params":{"month":"6","year":"2025"},"weekly_start_date_param":null,"weekly_end_date_param":null,"year_param":"2025","month_param":"6","calculated_weekly_start":"2025-07-06","calculated_weekly_end":"2025-07-12"} 
[2025-07-12 10:48:53] local.INFO: === REQUEST PARAMETERS DEBUG === {"all_request_params":{"month":"5","year":"2025"},"weekly_start_date_param":null,"weekly_end_date_param":null,"year_param":"2025","month_param":"5","calculated_weekly_start":"2025-07-06","calculated_weekly_end":"2025-07-12"} 
[2025-07-12 10:49:01] local.INFO: === REQUEST PARAMETERS DEBUG === {"all_request_params":{"weekly_start_date":"2025-05-11","weekly_end_date":"2025-05-17"},"weekly_start_date_param":"2025-05-11","weekly_end_date_param":"2025-05-17","year_param":null,"month_param":null,"calculated_weekly_start":"2025-05-11","calculated_weekly_end":"2025-05-17"} 
[2025-07-12 10:49:10] local.INFO: === REQUEST PARAMETERS DEBUG === {"all_request_params":{"month":"6","year":"2025"},"weekly_start_date_param":null,"weekly_end_date_param":null,"year_param":"2025","month_param":"6","calculated_weekly_start":"2025-07-06","calculated_weekly_end":"2025-07-12"} 
[2025-07-12 10:49:11] local.INFO: === REQUEST PARAMETERS DEBUG === {"all_request_params":{"month":"5","year":"2025"},"weekly_start_date_param":null,"weekly_end_date_param":null,"year_param":"2025","month_param":"5","calculated_weekly_start":"2025-07-06","calculated_weekly_end":"2025-07-12"} 
[2025-07-12 10:57:21] local.INFO: === REQUEST PARAMETERS DEBUG === {"all_request_params":[],"weekly_start_date_param":null,"weekly_end_date_param":null,"year_param":null,"month_param":null,"calculated_weekly_start":"2025-07-06","calculated_weekly_end":"2025-07-12"} 
[2025-07-12 10:58:21] local.INFO: === REQUEST PARAMETERS DEBUG === {"all_request_params":{"weekly_start_date":"2025-06-29","weekly_end_date":"2025-07-05"},"weekly_start_date_param":"2025-06-29","weekly_end_date_param":"2025-07-05","year_param":null,"month_param":null,"calculated_weekly_start":"2025-06-29","calculated_weekly_end":"2025-07-05"} 
[2025-07-12 11:03:26] local.INFO: === REQUEST PARAMETERS DEBUG === {"all_request_params":{"weekly_start_date":"2025-06-22","weekly_end_date":"2025-06-28"},"weekly_start_date_param":"2025-06-22","weekly_end_date_param":"2025-06-28","year_param":null,"month_param":null,"calculated_weekly_start":"2025-06-22","calculated_weekly_end":"2025-06-28"} 
[2025-07-12 11:03:52] local.INFO: === REQUEST PARAMETERS DEBUG === {"all_request_params":{"weekly_start_date":"2025-06-29","weekly_end_date":"2025-07-05"},"weekly_start_date_param":"2025-06-29","weekly_end_date_param":"2025-07-05","year_param":null,"month_param":null,"calculated_weekly_start":"2025-06-29","calculated_weekly_end":"2025-07-05"} 
[2025-07-12 11:04:02] local.INFO: === REQUEST PARAMETERS DEBUG === {"all_request_params":{"weekly_start_date":"2025-06-29","weekly_end_date":"2025-07-05"},"weekly_start_date_param":"2025-06-29","weekly_end_date_param":"2025-07-05","year_param":null,"month_param":null,"calculated_weekly_start":"2025-06-29","calculated_weekly_end":"2025-07-05"} 
[2025-07-12 11:04:22] local.INFO: === REQUEST PARAMETERS DEBUG === {"all_request_params":{"month":"8","year":"2025"},"weekly_start_date_param":null,"weekly_end_date_param":null,"year_param":"2025","month_param":"8","calculated_weekly_start":"2025-07-06","calculated_weekly_end":"2025-07-12"} 
[2025-07-12 11:04:26] local.INFO: === REQUEST PARAMETERS DEBUG === {"all_request_params":{"month":"7","year":"2025"},"weekly_start_date_param":null,"weekly_end_date_param":null,"year_param":"2025","month_param":"7","calculated_weekly_start":"2025-07-06","calculated_weekly_end":"2025-07-12"} 
[2025-07-12 11:04:28] local.INFO: === REQUEST PARAMETERS DEBUG === {"all_request_params":{"month":"6","year":"2025"},"weekly_start_date_param":null,"weekly_end_date_param":null,"year_param":"2025","month_param":"6","calculated_weekly_start":"2025-07-06","calculated_weekly_end":"2025-07-12"} 
[2025-07-12 11:04:41] local.INFO: === REQUEST PARAMETERS DEBUG === {"all_request_params":{"weekly_start_date":"2025-07-06","weekly_end_date":"2025-07-12"},"weekly_start_date_param":"2025-07-06","weekly_end_date_param":"2025-07-12","year_param":null,"month_param":null,"calculated_weekly_start":"2025-07-06","calculated_weekly_end":"2025-07-12"} 
[2025-07-12 11:05:31] local.INFO: === REQUEST PARAMETERS DEBUG === {"all_request_params":{"weekly_end_date":"2025-07-12","weekly_start_date":"2025-07-06"},"weekly_start_date_param":"2025-07-06","weekly_end_date_param":"2025-07-12","year_param":null,"month_param":null,"calculated_weekly_start":"2025-07-06","calculated_weekly_end":"2025-07-12"} 
[2025-07-12 11:05:38] local.INFO: === REQUEST PARAMETERS DEBUG === {"all_request_params":{"month":"8","year":"2025"},"weekly_start_date_param":null,"weekly_end_date_param":null,"year_param":"2025","month_param":"8","calculated_weekly_start":"2025-07-06","calculated_weekly_end":"2025-07-12"} 
[2025-07-12 11:05:40] local.INFO: === REQUEST PARAMETERS DEBUG === {"all_request_params":{"month":"7","year":"2025"},"weekly_start_date_param":null,"weekly_end_date_param":null,"year_param":"2025","month_param":"7","calculated_weekly_start":"2025-07-06","calculated_weekly_end":"2025-07-12"} 
[2025-07-12 11:05:46] local.INFO: === REQUEST PARAMETERS DEBUG === {"all_request_params":{"month":"6","year":"2025"},"weekly_start_date_param":null,"weekly_end_date_param":null,"year_param":"2025","month_param":"6","calculated_weekly_start":"2025-07-06","calculated_weekly_end":"2025-07-12"} 
[2025-07-12 11:05:48] local.INFO: === REQUEST PARAMETERS DEBUG === {"all_request_params":{"month":"7","year":"2025"},"weekly_start_date_param":null,"weekly_end_date_param":null,"year_param":"2025","month_param":"7","calculated_weekly_start":"2025-07-06","calculated_weekly_end":"2025-07-12"} 
[2025-07-12 11:06:41] local.INFO: === REQUEST PARAMETERS DEBUG === {"all_request_params":{"weekly_start_date":"2025-06-29","weekly_end_date":"2025-07-05"},"weekly_start_date_param":"2025-06-29","weekly_end_date_param":"2025-07-05","year_param":null,"month_param":null,"calculated_weekly_start":"2025-06-29","calculated_weekly_end":"2025-07-05"} 
[2025-07-12 11:11:50] local.INFO: === REQUEST PARAMETERS DEBUG === {"all_request_params":{"weekly_end_date":"2025-07-05","weekly_start_date":"2025-06-29"},"weekly_start_date_param":"2025-06-29","weekly_end_date_param":"2025-07-05","year_param":null,"month_param":null,"calculated_weekly_start":"2025-06-29","calculated_weekly_end":"2025-07-05"} 
[2025-07-12 11:12:13] local.INFO: === REQUEST PARAMETERS DEBUG === {"all_request_params":{"weekly_start_date":"2025-06-26","weekly_end_date":"2025-07-02"},"weekly_start_date_param":"2025-06-26","weekly_end_date_param":"2025-07-02","year_param":null,"month_param":null,"calculated_weekly_start":"2025-06-26","calculated_weekly_end":"2025-07-02"} 
[2025-07-12 11:12:29] local.INFO: === REQUEST PARAMETERS DEBUG === {"all_request_params":{"weekly_start_date":"2025-06-29","weekly_end_date":"2025-07-05"},"weekly_start_date_param":"2025-06-29","weekly_end_date_param":"2025-07-05","year_param":null,"month_param":null,"calculated_weekly_start":"2025-06-29","calculated_weekly_end":"2025-07-05"} 
[2025-07-12 11:12:36] local.INFO: === REQUEST PARAMETERS DEBUG === {"all_request_params":{"month":"6","year":"2025"},"weekly_start_date_param":null,"weekly_end_date_param":null,"year_param":"2025","month_param":"6","calculated_weekly_start":"2025-07-06","calculated_weekly_end":"2025-07-12"} 
[2025-07-12 11:12:38] local.INFO: === REQUEST PARAMETERS DEBUG === {"all_request_params":{"month":"7","year":"2025"},"weekly_start_date_param":null,"weekly_end_date_param":null,"year_param":"2025","month_param":"7","calculated_weekly_start":"2025-07-06","calculated_weekly_end":"2025-07-12"} 
[2025-07-12 11:12:39] local.INFO: === REQUEST PARAMETERS DEBUG === {"all_request_params":{"month":"6","year":"2025"},"weekly_start_date_param":null,"weekly_end_date_param":null,"year_param":"2025","month_param":"6","calculated_weekly_start":"2025-07-06","calculated_weekly_end":"2025-07-12"} 
[2025-07-12 11:12:40] local.INFO: === REQUEST PARAMETERS DEBUG === {"all_request_params":{"month":"7","year":"2025"},"weekly_start_date_param":null,"weekly_end_date_param":null,"year_param":"2025","month_param":"7","calculated_weekly_start":"2025-07-06","calculated_weekly_end":"2025-07-12"} 
[2025-07-12 11:12:41] local.INFO: === REQUEST PARAMETERS DEBUG === {"all_request_params":{"month":"8","year":"2025"},"weekly_start_date_param":null,"weekly_end_date_param":null,"year_param":"2025","month_param":"8","calculated_weekly_start":"2025-07-06","calculated_weekly_end":"2025-07-12"} 
[2025-07-12 11:12:42] local.INFO: === REQUEST PARAMETERS DEBUG === {"all_request_params":{"month":"7","year":"2025"},"weekly_start_date_param":null,"weekly_end_date_param":null,"year_param":"2025","month_param":"7","calculated_weekly_start":"2025-07-06","calculated_weekly_end":"2025-07-12"} 
[2025-07-12 11:12:48] local.INFO: === REQUEST PARAMETERS DEBUG === {"all_request_params":{"weekly_start_date":"2025-07-06","weekly_end_date":"2025-07-12"},"weekly_start_date_param":"2025-07-06","weekly_end_date_param":"2025-07-12","year_param":null,"month_param":null,"calculated_weekly_start":"2025-07-06","calculated_weekly_end":"2025-07-12"} 
[2025-07-12 11:12:55] local.INFO: === REQUEST PARAMETERS DEBUG === {"all_request_params":{"weekly_end_date":"2025-07-12","weekly_start_date":"2025-07-06"},"weekly_start_date_param":"2025-07-06","weekly_end_date_param":"2025-07-12","year_param":null,"month_param":null,"calculated_weekly_start":"2025-07-06","calculated_weekly_end":"2025-07-12"} 
[2025-07-12 11:13:01] local.INFO: === REQUEST PARAMETERS DEBUG === {"all_request_params":{"month":"6","year":"2025"},"weekly_start_date_param":null,"weekly_end_date_param":null,"year_param":"2025","month_param":"6","calculated_weekly_start":"2025-07-06","calculated_weekly_end":"2025-07-12"} 
[2025-07-12 11:13:03] local.INFO: === REQUEST PARAMETERS DEBUG === {"all_request_params":{"month":"7","year":"2025"},"weekly_start_date_param":null,"weekly_end_date_param":null,"year_param":"2025","month_param":"7","calculated_weekly_start":"2025-07-06","calculated_weekly_end":"2025-07-12"} 
[2025-07-12 11:13:05] local.INFO: === REQUEST PARAMETERS DEBUG === {"all_request_params":{"month":"8","year":"2025"},"weekly_start_date_param":null,"weekly_end_date_param":null,"year_param":"2025","month_param":"8","calculated_weekly_start":"2025-07-06","calculated_weekly_end":"2025-07-12"} 
[2025-07-12 11:13:06] local.INFO: === REQUEST PARAMETERS DEBUG === {"all_request_params":{"month":"7","year":"2025"},"weekly_start_date_param":null,"weekly_end_date_param":null,"year_param":"2025","month_param":"7","calculated_weekly_start":"2025-07-06","calculated_weekly_end":"2025-07-12"} 
[2025-07-12 11:13:09] local.INFO: === REQUEST PARAMETERS DEBUG === {"all_request_params":{"month":"6","year":"2025"},"weekly_start_date_param":null,"weekly_end_date_param":null,"year_param":"2025","month_param":"6","calculated_weekly_start":"2025-07-06","calculated_weekly_end":"2025-07-12"} 
[2025-07-12 11:13:11] local.INFO: === REQUEST PARAMETERS DEBUG === {"all_request_params":{"month":"7","year":"2025"},"weekly_start_date_param":null,"weekly_end_date_param":null,"year_param":"2025","month_param":"7","calculated_weekly_start":"2025-07-06","calculated_weekly_end":"2025-07-12"} 
[2025-07-12 11:13:17] local.INFO: === REQUEST PARAMETERS DEBUG === {"all_request_params":{"weekly_start_date":"2025-06-29","weekly_end_date":"2025-07-05"},"weekly_start_date_param":"2025-06-29","weekly_end_date_param":"2025-07-05","year_param":null,"month_param":null,"calculated_weekly_start":"2025-06-29","calculated_weekly_end":"2025-07-05"} 
[2025-07-12 11:13:26] local.INFO: === REQUEST PARAMETERS DEBUG === {"all_request_params":{"month":"8","year":"2025"},"weekly_start_date_param":null,"weekly_end_date_param":null,"year_param":"2025","month_param":"8","calculated_weekly_start":"2025-07-06","calculated_weekly_end":"2025-07-12"} 
[2025-07-12 11:15:44] local.INFO: === REQUEST PARAMETERS DEBUG === {"all_request_params":{"weekly_start_date":"2025-06-29","weekly_end_date":"2025-07-05"},"weekly_start_date_param":"2025-06-29","weekly_end_date_param":"2025-07-05","year_param":null,"month_param":null,"calculated_weekly_start":"2025-06-29","calculated_weekly_end":"2025-07-05"} 
[2025-07-12 11:20:13] local.INFO: === REQUEST PARAMETERS DEBUG === {"all_request_params":[],"weekly_start_date_param":null,"weekly_end_date_param":null,"year_param":null,"month_param":null,"calculated_weekly_start":"2025-07-06","calculated_weekly_end":"2025-07-12"} 
[2025-07-12 11:20:29] local.INFO: === REQUEST PARAMETERS DEBUG === {"all_request_params":{"weekly_start_date":"2025-06-29","weekly_end_date":"2025-07-05"},"weekly_start_date_param":"2025-06-29","weekly_end_date_param":"2025-07-05","year_param":null,"month_param":null,"calculated_weekly_start":"2025-06-29","calculated_weekly_end":"2025-07-05"} 
[2025-07-12 11:22:07] local.INFO: === REQUEST PARAMETERS DEBUG === {"all_request_params":{"weekly_end_date":"2025-07-05","weekly_start_date":"2025-06-29"},"weekly_start_date_param":"2025-06-29","weekly_end_date_param":"2025-07-05","year_param":null,"month_param":null,"calculated_weekly_start":"2025-06-29","calculated_weekly_end":"2025-07-05"} 
[2025-07-12 11:22:25] local.INFO: === REQUEST PARAMETERS DEBUG === {"all_request_params":{"weekly_start_date":"2025-07-06","weekly_end_date":"2025-07-12"},"weekly_start_date_param":"2025-07-06","weekly_end_date_param":"2025-07-12","year_param":null,"month_param":null,"calculated_weekly_start":"2025-07-06","calculated_weekly_end":"2025-07-12"} 
[2025-07-12 11:22:34] local.INFO: === REQUEST PARAMETERS DEBUG === {"all_request_params":{"weekly_end_date":"2025-07-12","weekly_start_date":"2025-07-06"},"weekly_start_date_param":"2025-07-06","weekly_end_date_param":"2025-07-12","year_param":null,"month_param":null,"calculated_weekly_start":"2025-07-06","calculated_weekly_end":"2025-07-12"} 
[2025-07-12 11:22:50] local.INFO: === REQUEST PARAMETERS DEBUG === {"all_request_params":{"weekly_start_date":"2025-06-29","weekly_end_date":"2025-07-05"},"weekly_start_date_param":"2025-06-29","weekly_end_date_param":"2025-07-05","year_param":null,"month_param":null,"calculated_weekly_start":"2025-06-29","calculated_weekly_end":"2025-07-05"} 
[2025-07-12 11:31:08] local.INFO: === REQUEST PARAMETERS DEBUG === {"all_request_params":{"weekly_end_date":"2025-07-05","weekly_start_date":"2025-06-29"},"weekly_start_date_param":"2025-06-29","weekly_end_date_param":"2025-07-05","year_param":null,"month_param":null,"calculated_weekly_start":"2025-06-29","calculated_weekly_end":"2025-07-05"} 
[2025-07-12 11:31:08] local.INFO: Working Days Debug {"workingDays":["Sun","Mon","Tue","Wed","Thu","Fri","Sat"],"weeklyQueryStart":"2025-06-29","weeklyQueryEnd":"2025-07-05"} 
[2025-07-12 11:31:08] local.INFO: Undertime Debug - Day: Sun, Date: 2025-06-29, Has Attendance: Yes, Undertime Minutes: 22, Has Undertime: Yes  
[2025-07-12 11:31:08] local.INFO: Undertime Debug - Day: Mon, Date: 2025-06-30, Has Attendance: Yes, Undertime Minutes: 0, Has Undertime: No  
[2025-07-12 11:31:08] local.INFO: Undertime Debug - Day: Tue, Date: 2025-07-01, Has Attendance: Yes, Undertime Minutes: 0, Has Undertime: No  
[2025-07-12 11:31:08] local.INFO: Undertime Debug - Day: Wed, Date: 2025-07-02, Has Attendance: Yes, Undertime Minutes: 0, Has Undertime: No  
[2025-07-12 11:31:08] local.INFO: Undertime Debug - Day: Thu, Date: 2025-07-03, Has Attendance: Yes, Undertime Minutes: 0, Has Undertime: No  
[2025-07-12 11:31:08] local.INFO: Undertime Debug - Day: Fri, Date: 2025-07-04, Has Attendance: Yes, Undertime Minutes: 0, Has Undertime: No  
[2025-07-12 11:31:08] local.INFO: Undertime Debug - Day: Sat, Date: 2025-07-05, Has Attendance: Yes, Undertime Minutes: 0, Has Undertime: No  
[2025-07-12 11:31:30] local.INFO: === REQUEST PARAMETERS DEBUG === {"all_request_params":{"weekly_start_date":"2025-07-06","weekly_end_date":"2025-07-12"},"weekly_start_date_param":"2025-07-06","weekly_end_date_param":"2025-07-12","year_param":null,"month_param":null,"calculated_weekly_start":"2025-07-06","calculated_weekly_end":"2025-07-12"} 
[2025-07-12 11:31:30] local.INFO: Working Days Debug {"workingDays":["Sun","Mon","Tue","Wed","Thu","Fri","Sat"],"weeklyQueryStart":"2025-07-06","weeklyQueryEnd":"2025-07-12"} 
[2025-07-12 11:31:30] local.INFO: Undertime Debug - Day: Sun, Date: 2025-07-06, Has Attendance: Yes, Undertime Minutes: 0, Has Undertime: No  
[2025-07-12 11:31:30] local.INFO: Undertime Debug - Day: Mon, Date: 2025-07-07, Has Attendance: Yes, Undertime Minutes: 0, Has Undertime: No  
[2025-07-12 11:31:30] local.INFO: Undertime Debug - Day: Tue, Date: 2025-07-08, Has Attendance: Yes, Undertime Minutes: 60, Has Undertime: Yes  
[2025-07-12 11:31:30] local.INFO: Undertime Debug - Day: Wed, Date: 2025-07-09, Has Attendance: Yes, Undertime Minutes: 83, Has Undertime: Yes  
[2025-07-12 11:31:30] local.INFO: Undertime Debug - Day: Thu, Date: 2025-07-10, Has Attendance: Yes, Undertime Minutes: 36, Has Undertime: Yes  
[2025-07-12 11:31:30] local.INFO: Undertime Debug - Day: Fri, Date: 2025-07-11, Has Attendance: Yes, Undertime Minutes: 0, Has Undertime: No  
[2025-07-12 11:31:30] local.INFO: Undertime Debug - Day: Sat, Date: 2025-07-12, Has Attendance: Yes, Undertime Minutes: 0, Has Undertime: No  
[2025-07-12 11:31:40] local.INFO: === REQUEST PARAMETERS DEBUG === {"all_request_params":{"weekly_start_date":"2025-06-29","weekly_end_date":"2025-07-05"},"weekly_start_date_param":"2025-06-29","weekly_end_date_param":"2025-07-05","year_param":null,"month_param":null,"calculated_weekly_start":"2025-06-29","calculated_weekly_end":"2025-07-05"} 
[2025-07-12 11:31:40] local.INFO: Working Days Debug {"workingDays":["Sun","Mon","Tue","Wed","Thu","Fri","Sat"],"weeklyQueryStart":"2025-06-29","weeklyQueryEnd":"2025-07-05"} 
[2025-07-12 11:31:40] local.INFO: Undertime Debug - Day: Sun, Date: 2025-06-29, Has Attendance: Yes, Undertime Minutes: 22, Has Undertime: Yes  
[2025-07-12 11:31:40] local.INFO: Undertime Debug - Day: Mon, Date: 2025-06-30, Has Attendance: Yes, Undertime Minutes: 0, Has Undertime: No  
[2025-07-12 11:31:40] local.INFO: Undertime Debug - Day: Tue, Date: 2025-07-01, Has Attendance: Yes, Undertime Minutes: 0, Has Undertime: No  
[2025-07-12 11:31:40] local.INFO: Undertime Debug - Day: Wed, Date: 2025-07-02, Has Attendance: Yes, Undertime Minutes: 0, Has Undertime: No  
[2025-07-12 11:31:40] local.INFO: Undertime Debug - Day: Thu, Date: 2025-07-03, Has Attendance: Yes, Undertime Minutes: 0, Has Undertime: No  
[2025-07-12 11:31:40] local.INFO: Undertime Debug - Day: Fri, Date: 2025-07-04, Has Attendance: Yes, Undertime Minutes: 0, Has Undertime: No  
[2025-07-12 11:31:40] local.INFO: Undertime Debug - Day: Sat, Date: 2025-07-05, Has Attendance: Yes, Undertime Minutes: 0, Has Undertime: No  
[2025-07-12 11:31:56] local.INFO: === REQUEST PARAMETERS DEBUG === {"all_request_params":{"weekly_start_date":"2025-07-06","weekly_end_date":"2025-07-12"},"weekly_start_date_param":"2025-07-06","weekly_end_date_param":"2025-07-12","year_param":null,"month_param":null,"calculated_weekly_start":"2025-07-06","calculated_weekly_end":"2025-07-12"} 
[2025-07-12 11:31:56] local.INFO: Working Days Debug {"workingDays":["Sun","Mon","Tue","Wed","Thu","Fri","Sat"],"weeklyQueryStart":"2025-07-06","weeklyQueryEnd":"2025-07-12"} 
[2025-07-12 11:31:56] local.INFO: Undertime Debug - Day: Sun, Date: 2025-07-06, Has Attendance: Yes, Undertime Minutes: 0, Has Undertime: No  
[2025-07-12 11:31:56] local.INFO: Undertime Debug - Day: Mon, Date: 2025-07-07, Has Attendance: Yes, Undertime Minutes: 0, Has Undertime: No  
[2025-07-12 11:31:56] local.INFO: Undertime Debug - Day: Tue, Date: 2025-07-08, Has Attendance: Yes, Undertime Minutes: 60, Has Undertime: Yes  
[2025-07-12 11:31:56] local.INFO: Undertime Debug - Day: Wed, Date: 2025-07-09, Has Attendance: Yes, Undertime Minutes: 83, Has Undertime: Yes  
[2025-07-12 11:31:56] local.INFO: Undertime Debug - Day: Thu, Date: 2025-07-10, Has Attendance: Yes, Undertime Minutes: 36, Has Undertime: Yes  
[2025-07-12 11:31:56] local.INFO: Undertime Debug - Day: Fri, Date: 2025-07-11, Has Attendance: Yes, Undertime Minutes: 0, Has Undertime: No  
[2025-07-12 11:31:56] local.INFO: Undertime Debug - Day: Sat, Date: 2025-07-12, Has Attendance: Yes, Undertime Minutes: 0, Has Undertime: No  
[2025-07-12 11:32:06] local.INFO: === REQUEST PARAMETERS DEBUG === {"all_request_params":{"weekly_end_date":"2025-07-12","weekly_start_date":"2025-07-06"},"weekly_start_date_param":"2025-07-06","weekly_end_date_param":"2025-07-12","year_param":null,"month_param":null,"calculated_weekly_start":"2025-07-06","calculated_weekly_end":"2025-07-12"} 
[2025-07-12 11:32:06] local.INFO: Working Days Debug {"workingDays":["Sun","Mon","Tue","Wed","Thu","Fri","Sat"],"weeklyQueryStart":"2025-07-06","weeklyQueryEnd":"2025-07-12"} 
[2025-07-12 11:32:06] local.INFO: Undertime Debug - Day: Sun, Date: 2025-07-06, Has Attendance: Yes, Undertime Minutes: 0, Has Undertime: No  
[2025-07-12 11:32:06] local.INFO: Undertime Debug - Day: Mon, Date: 2025-07-07, Has Attendance: Yes, Undertime Minutes: 0, Has Undertime: No  
[2025-07-12 11:32:06] local.INFO: Undertime Debug - Day: Tue, Date: 2025-07-08, Has Attendance: Yes, Undertime Minutes: 60, Has Undertime: Yes  
[2025-07-12 11:32:06] local.INFO: Undertime Debug - Day: Wed, Date: 2025-07-09, Has Attendance: Yes, Undertime Minutes: 83, Has Undertime: Yes  
[2025-07-12 11:32:06] local.INFO: Undertime Debug - Day: Thu, Date: 2025-07-10, Has Attendance: Yes, Undertime Minutes: 36, Has Undertime: Yes  
[2025-07-12 11:32:06] local.INFO: Undertime Debug - Day: Fri, Date: 2025-07-11, Has Attendance: Yes, Undertime Minutes: 0, Has Undertime: No  
[2025-07-12 11:32:06] local.INFO: Undertime Debug - Day: Sat, Date: 2025-07-12, Has Attendance: Yes, Undertime Minutes: 0, Has Undertime: No  
[2025-07-12 11:32:49] local.INFO: === REQUEST PARAMETERS DEBUG === {"all_request_params":{"weekly_start_date":"2025-06-29","weekly_end_date":"2025-07-05"},"weekly_start_date_param":"2025-06-29","weekly_end_date_param":"2025-07-05","year_param":null,"month_param":null,"calculated_weekly_start":"2025-06-29","calculated_weekly_end":"2025-07-05"} 
[2025-07-12 11:32:49] local.INFO: Working Days Debug {"workingDays":["Sun","Mon","Tue","Wed","Thu","Fri","Sat"],"weeklyQueryStart":"2025-06-29","weeklyQueryEnd":"2025-07-05"} 
[2025-07-12 11:32:49] local.INFO: Undertime Debug - Day: Sun, Date: 2025-06-29, Has Attendance: Yes, Undertime Minutes: 22, Has Undertime: Yes  
[2025-07-12 11:32:49] local.INFO: Undertime Debug - Day: Mon, Date: 2025-06-30, Has Attendance: Yes, Undertime Minutes: 0, Has Undertime: No  
[2025-07-12 11:32:49] local.INFO: Undertime Debug - Day: Tue, Date: 2025-07-01, Has Attendance: Yes, Undertime Minutes: 0, Has Undertime: No  
[2025-07-12 11:32:49] local.INFO: Undertime Debug - Day: Wed, Date: 2025-07-02, Has Attendance: Yes, Undertime Minutes: 0, Has Undertime: No  
[2025-07-12 11:32:49] local.INFO: Undertime Debug - Day: Thu, Date: 2025-07-03, Has Attendance: Yes, Undertime Minutes: 0, Has Undertime: No  
[2025-07-12 11:32:49] local.INFO: Undertime Debug - Day: Fri, Date: 2025-07-04, Has Attendance: Yes, Undertime Minutes: 0, Has Undertime: No  
[2025-07-12 11:32:49] local.INFO: Undertime Debug - Day: Sat, Date: 2025-07-05, Has Attendance: Yes, Undertime Minutes: 0, Has Undertime: No  
[2025-07-12 11:32:51] local.INFO: === REQUEST PARAMETERS DEBUG === {"all_request_params":{"weekly_end_date":"2025-07-05","weekly_start_date":"2025-06-29"},"weekly_start_date_param":"2025-06-29","weekly_end_date_param":"2025-07-05","year_param":null,"month_param":null,"calculated_weekly_start":"2025-06-29","calculated_weekly_end":"2025-07-05"} 
[2025-07-12 11:32:52] local.INFO: Working Days Debug {"workingDays":["Sun","Mon","Tue","Wed","Thu","Fri","Sat"],"weeklyQueryStart":"2025-06-29","weeklyQueryEnd":"2025-07-05"} 
[2025-07-12 11:32:52] local.INFO: Undertime Debug - Day: Sun, Date: 2025-06-29, Has Attendance: Yes, Undertime Minutes: 22, Has Undertime: Yes  
[2025-07-12 11:32:52] local.INFO: Undertime Debug - Day: Mon, Date: 2025-06-30, Has Attendance: Yes, Undertime Minutes: 0, Has Undertime: No  
[2025-07-12 11:32:52] local.INFO: Undertime Debug - Day: Tue, Date: 2025-07-01, Has Attendance: Yes, Undertime Minutes: 0, Has Undertime: No  
[2025-07-12 11:32:52] local.INFO: Undertime Debug - Day: Wed, Date: 2025-07-02, Has Attendance: Yes, Undertime Minutes: 0, Has Undertime: No  
[2025-07-12 11:32:52] local.INFO: Undertime Debug - Day: Thu, Date: 2025-07-03, Has Attendance: Yes, Undertime Minutes: 0, Has Undertime: No  
[2025-07-12 11:32:52] local.INFO: Undertime Debug - Day: Fri, Date: 2025-07-04, Has Attendance: Yes, Undertime Minutes: 0, Has Undertime: No  
[2025-07-12 11:32:52] local.INFO: Undertime Debug - Day: Sat, Date: 2025-07-05, Has Attendance: Yes, Undertime Minutes: 0, Has Undertime: No  
[2025-07-12 11:34:13] local.INFO: === REQUEST PARAMETERS DEBUG === {"all_request_params":{"weekly_start_date":"2025-07-06","weekly_end_date":"2025-07-12"},"weekly_start_date_param":"2025-07-06","weekly_end_date_param":"2025-07-12","year_param":null,"month_param":null,"calculated_weekly_start":"2025-07-06","calculated_weekly_end":"2025-07-12"} 
[2025-07-12 11:34:13] local.INFO: Working Days Debug {"workingDays":["Sun","Mon","Tue","Wed","Thu","Fri","Sat"],"weeklyQueryStart":"2025-07-06","weeklyQueryEnd":"2025-07-12"} 
[2025-07-12 11:34:13] local.INFO: Undertime Debug - Day: Sun, Date: 2025-07-06, Has Attendance: Yes, Undertime Minutes: 0, Has Undertime: No  
[2025-07-12 11:34:13] local.INFO: Undertime Debug - Day: Mon, Date: 2025-07-07, Has Attendance: Yes, Undertime Minutes: 0, Has Undertime: No  
[2025-07-12 11:34:13] local.INFO: Undertime Debug - Day: Tue, Date: 2025-07-08, Has Attendance: Yes, Undertime Minutes: 60, Has Undertime: Yes  
[2025-07-12 11:34:13] local.INFO: Undertime Debug - Day: Wed, Date: 2025-07-09, Has Attendance: Yes, Undertime Minutes: 83, Has Undertime: Yes  
[2025-07-12 11:34:13] local.INFO: Undertime Debug - Day: Thu, Date: 2025-07-10, Has Attendance: Yes, Undertime Minutes: 36, Has Undertime: Yes  
[2025-07-12 11:34:13] local.INFO: Undertime Debug - Day: Fri, Date: 2025-07-11, Has Attendance: Yes, Undertime Minutes: 0, Has Undertime: No  
[2025-07-12 11:34:13] local.INFO: Undertime Debug - Day: Sat, Date: 2025-07-12, Has Attendance: Yes, Undertime Minutes: 0, Has Undertime: No  
[2025-07-12 11:34:16] local.INFO: === REQUEST PARAMETERS DEBUG === {"all_request_params":{"weekly_end_date":"2025-07-12","weekly_start_date":"2025-07-06"},"weekly_start_date_param":"2025-07-06","weekly_end_date_param":"2025-07-12","year_param":null,"month_param":null,"calculated_weekly_start":"2025-07-06","calculated_weekly_end":"2025-07-12"} 
[2025-07-12 11:34:16] local.INFO: Working Days Debug {"workingDays":["Sun","Mon","Tue","Wed","Thu","Fri","Sat"],"weeklyQueryStart":"2025-07-06","weeklyQueryEnd":"2025-07-12"} 
[2025-07-12 11:34:16] local.INFO: Undertime Debug - Day: Sun, Date: 2025-07-06, Has Attendance: Yes, Undertime Minutes: 0, Has Undertime: No  
[2025-07-12 11:34:16] local.INFO: Undertime Debug - Day: Mon, Date: 2025-07-07, Has Attendance: Yes, Undertime Minutes: 0, Has Undertime: No  
[2025-07-12 11:34:16] local.INFO: Undertime Debug - Day: Tue, Date: 2025-07-08, Has Attendance: Yes, Undertime Minutes: 60, Has Undertime: Yes  
[2025-07-12 11:34:16] local.INFO: Undertime Debug - Day: Wed, Date: 2025-07-09, Has Attendance: Yes, Undertime Minutes: 83, Has Undertime: Yes  
[2025-07-12 11:34:16] local.INFO: Undertime Debug - Day: Thu, Date: 2025-07-10, Has Attendance: Yes, Undertime Minutes: 36, Has Undertime: Yes  
[2025-07-12 11:34:16] local.INFO: Undertime Debug - Day: Fri, Date: 2025-07-11, Has Attendance: Yes, Undertime Minutes: 0, Has Undertime: No  
[2025-07-12 11:34:16] local.INFO: Undertime Debug - Day: Sat, Date: 2025-07-12, Has Attendance: Yes, Undertime Minutes: 0, Has Undertime: No  
[2025-07-12 11:36:52] local.INFO: === REQUEST PARAMETERS DEBUG === {"all_request_params":{"weekly_end_date":"2025-07-12","weekly_start_date":"2025-07-06"},"weekly_start_date_param":"2025-07-06","weekly_end_date_param":"2025-07-12","year_param":null,"month_param":null,"calculated_weekly_start":"2025-07-06","calculated_weekly_end":"2025-07-12"} 
[2025-07-12 11:36:52] local.INFO: Working Days Debug {"workingDays":["Sun","Mon","Tue","Wed","Thu","Fri","Sat"],"weeklyQueryStart":"2025-07-06","weeklyQueryEnd":"2025-07-12"} 
[2025-07-12 11:36:52] local.INFO: Undertime Debug - Day: Sun, Date: 2025-07-06, Has Attendance: Yes, Undertime Minutes: 0, Has Undertime: No  
[2025-07-12 11:36:52] local.INFO: Undertime Debug - Day: Mon, Date: 2025-07-07, Has Attendance: Yes, Undertime Minutes: 0, Has Undertime: No  
[2025-07-12 11:36:52] local.INFO: Undertime Debug - Day: Tue, Date: 2025-07-08, Has Attendance: Yes, Undertime Minutes: 60, Has Undertime: Yes  
[2025-07-12 11:36:52] local.INFO: Undertime Debug - Day: Wed, Date: 2025-07-09, Has Attendance: Yes, Undertime Minutes: 83, Has Undertime: Yes  
[2025-07-12 11:36:52] local.INFO: Undertime Debug - Day: Thu, Date: 2025-07-10, Has Attendance: Yes, Undertime Minutes: 36, Has Undertime: Yes  
[2025-07-12 11:36:52] local.INFO: Undertime Debug - Day: Fri, Date: 2025-07-11, Has Attendance: Yes, Undertime Minutes: 0, Has Undertime: No  
[2025-07-12 11:36:52] local.INFO: Undertime Debug - Day: Sat, Date: 2025-07-12, Has Attendance: Yes, Undertime Minutes: 0, Has Undertime: No  
[2025-07-12 11:37:01] local.INFO: === REQUEST PARAMETERS DEBUG === {"all_request_params":{"weekly_start_date":"2025-06-29","weekly_end_date":"2025-07-05"},"weekly_start_date_param":"2025-06-29","weekly_end_date_param":"2025-07-05","year_param":null,"month_param":null,"calculated_weekly_start":"2025-06-29","calculated_weekly_end":"2025-07-05"} 
[2025-07-12 11:37:01] local.INFO: Working Days Debug {"workingDays":["Sun","Mon","Tue","Wed","Thu","Fri","Sat"],"weeklyQueryStart":"2025-06-29","weeklyQueryEnd":"2025-07-05"} 
[2025-07-12 11:37:01] local.INFO: Undertime Debug - Day: Sun, Date: 2025-06-29, Has Attendance: Yes, Undertime Minutes: 22, Has Undertime: Yes  
[2025-07-12 11:37:01] local.INFO: Undertime Debug - Day: Mon, Date: 2025-06-30, Has Attendance: Yes, Undertime Minutes: 0, Has Undertime: No  
[2025-07-12 11:37:01] local.INFO: Undertime Debug - Day: Tue, Date: 2025-07-01, Has Attendance: Yes, Undertime Minutes: 0, Has Undertime: No  
[2025-07-12 11:37:01] local.INFO: Undertime Debug - Day: Wed, Date: 2025-07-02, Has Attendance: Yes, Undertime Minutes: 0, Has Undertime: No  
[2025-07-12 11:37:01] local.INFO: Undertime Debug - Day: Thu, Date: 2025-07-03, Has Attendance: Yes, Undertime Minutes: 0, Has Undertime: No  
[2025-07-12 11:37:01] local.INFO: Undertime Debug - Day: Fri, Date: 2025-07-04, Has Attendance: Yes, Undertime Minutes: 0, Has Undertime: No  
[2025-07-12 11:37:01] local.INFO: Undertime Debug - Day: Sat, Date: 2025-07-05, Has Attendance: Yes, Undertime Minutes: 0, Has Undertime: No  
[2025-07-12 11:37:15] local.INFO: === REQUEST PARAMETERS DEBUG === {"all_request_params":{"month":"8","year":"2025","weekly_start_date":"2025-06-29","weekly_end_date":"2025-07-05"},"weekly_start_date_param":"2025-06-29","weekly_end_date_param":"2025-07-05","year_param":"2025","month_param":"8","calculated_weekly_start":"2025-06-29","calculated_weekly_end":"2025-07-05"} 
[2025-07-12 11:37:15] local.INFO: Working Days Debug {"workingDays":["Sun","Mon","Tue","Wed","Thu","Fri","Sat"],"weeklyQueryStart":"2025-06-29","weeklyQueryEnd":"2025-07-05"} 
[2025-07-12 11:37:15] local.INFO: Undertime Debug - Day: Sun, Date: 2025-06-29, Has Attendance: Yes, Undertime Minutes: 22, Has Undertime: Yes  
[2025-07-12 11:37:15] local.INFO: Undertime Debug - Day: Mon, Date: 2025-06-30, Has Attendance: Yes, Undertime Minutes: 0, Has Undertime: No  
[2025-07-12 11:37:15] local.INFO: Undertime Debug - Day: Tue, Date: 2025-07-01, Has Attendance: Yes, Undertime Minutes: 0, Has Undertime: No  
[2025-07-12 11:37:15] local.INFO: Undertime Debug - Day: Wed, Date: 2025-07-02, Has Attendance: Yes, Undertime Minutes: 0, Has Undertime: No  
[2025-07-12 11:37:15] local.INFO: Undertime Debug - Day: Thu, Date: 2025-07-03, Has Attendance: Yes, Undertime Minutes: 0, Has Undertime: No  
[2025-07-12 11:37:15] local.INFO: Undertime Debug - Day: Fri, Date: 2025-07-04, Has Attendance: Yes, Undertime Minutes: 0, Has Undertime: No  
[2025-07-12 11:37:15] local.INFO: Undertime Debug - Day: Sat, Date: 2025-07-05, Has Attendance: Yes, Undertime Minutes: 0, Has Undertime: No  
[2025-07-12 11:37:17] local.INFO: === REQUEST PARAMETERS DEBUG === {"all_request_params":{"month":"7","year":"2025","weekly_start_date":"2025-06-29","weekly_end_date":"2025-07-05"},"weekly_start_date_param":"2025-06-29","weekly_end_date_param":"2025-07-05","year_param":"2025","month_param":"7","calculated_weekly_start":"2025-06-29","calculated_weekly_end":"2025-07-05"} 
[2025-07-12 11:37:17] local.INFO: Working Days Debug {"workingDays":["Sun","Mon","Tue","Wed","Thu","Fri","Sat"],"weeklyQueryStart":"2025-06-29","weeklyQueryEnd":"2025-07-05"} 
[2025-07-12 11:37:17] local.INFO: Undertime Debug - Day: Sun, Date: 2025-06-29, Has Attendance: Yes, Undertime Minutes: 22, Has Undertime: Yes  
[2025-07-12 11:37:17] local.INFO: Undertime Debug - Day: Mon, Date: 2025-06-30, Has Attendance: Yes, Undertime Minutes: 0, Has Undertime: No  
[2025-07-12 11:37:17] local.INFO: Undertime Debug - Day: Tue, Date: 2025-07-01, Has Attendance: Yes, Undertime Minutes: 0, Has Undertime: No  
[2025-07-12 11:37:17] local.INFO: Undertime Debug - Day: Wed, Date: 2025-07-02, Has Attendance: Yes, Undertime Minutes: 0, Has Undertime: No  
[2025-07-12 11:37:17] local.INFO: Undertime Debug - Day: Thu, Date: 2025-07-03, Has Attendance: Yes, Undertime Minutes: 0, Has Undertime: No  
[2025-07-12 11:37:17] local.INFO: Undertime Debug - Day: Fri, Date: 2025-07-04, Has Attendance: Yes, Undertime Minutes: 0, Has Undertime: No  
[2025-07-12 11:37:17] local.INFO: Undertime Debug - Day: Sat, Date: 2025-07-05, Has Attendance: Yes, Undertime Minutes: 0, Has Undertime: No  
[2025-07-12 11:37:19] local.INFO: === REQUEST PARAMETERS DEBUG === {"all_request_params":{"month":"6","year":"2025","weekly_start_date":"2025-06-29","weekly_end_date":"2025-07-05"},"weekly_start_date_param":"2025-06-29","weekly_end_date_param":"2025-07-05","year_param":"2025","month_param":"6","calculated_weekly_start":"2025-06-29","calculated_weekly_end":"2025-07-05"} 
[2025-07-12 11:37:19] local.INFO: Working Days Debug {"workingDays":["Sun","Mon","Tue","Wed","Thu","Fri","Sat"],"weeklyQueryStart":"2025-06-29","weeklyQueryEnd":"2025-07-05"} 
[2025-07-12 11:37:19] local.INFO: Undertime Debug - Day: Sun, Date: 2025-06-29, Has Attendance: Yes, Undertime Minutes: 22, Has Undertime: Yes  
[2025-07-12 11:37:19] local.INFO: Undertime Debug - Day: Mon, Date: 2025-06-30, Has Attendance: Yes, Undertime Minutes: 0, Has Undertime: No  
[2025-07-12 11:37:19] local.INFO: Undertime Debug - Day: Tue, Date: 2025-07-01, Has Attendance: Yes, Undertime Minutes: 0, Has Undertime: No  
[2025-07-12 11:37:19] local.INFO: Undertime Debug - Day: Wed, Date: 2025-07-02, Has Attendance: Yes, Undertime Minutes: 0, Has Undertime: No  
[2025-07-12 11:37:19] local.INFO: Undertime Debug - Day: Thu, Date: 2025-07-03, Has Attendance: Yes, Undertime Minutes: 0, Has Undertime: No  
[2025-07-12 11:37:19] local.INFO: Undertime Debug - Day: Fri, Date: 2025-07-04, Has Attendance: Yes, Undertime Minutes: 0, Has Undertime: No  
[2025-07-12 11:37:19] local.INFO: Undertime Debug - Day: Sat, Date: 2025-07-05, Has Attendance: Yes, Undertime Minutes: 0, Has Undertime: No  
[2025-07-12 11:37:22] local.INFO: === REQUEST PARAMETERS DEBUG === {"all_request_params":{"weekly_start_date":"2025-06-29","weekly_end_date":"2025-07-05","month":"6","year":"2025"},"weekly_start_date_param":"2025-06-29","weekly_end_date_param":"2025-07-05","year_param":"2025","month_param":"6","calculated_weekly_start":"2025-06-29","calculated_weekly_end":"2025-07-05"} 
[2025-07-12 11:37:22] local.INFO: Working Days Debug {"workingDays":["Sun","Mon","Tue","Wed","Thu","Fri","Sat"],"weeklyQueryStart":"2025-06-29","weeklyQueryEnd":"2025-07-05"} 
[2025-07-12 11:37:22] local.INFO: Undertime Debug - Day: Sun, Date: 2025-06-29, Has Attendance: Yes, Undertime Minutes: 22, Has Undertime: Yes  
[2025-07-12 11:37:22] local.INFO: Undertime Debug - Day: Mon, Date: 2025-06-30, Has Attendance: Yes, Undertime Minutes: 0, Has Undertime: No  
[2025-07-12 11:37:22] local.INFO: Undertime Debug - Day: Tue, Date: 2025-07-01, Has Attendance: Yes, Undertime Minutes: 0, Has Undertime: No  
[2025-07-12 11:37:22] local.INFO: Undertime Debug - Day: Wed, Date: 2025-07-02, Has Attendance: Yes, Undertime Minutes: 0, Has Undertime: No  
[2025-07-12 11:37:22] local.INFO: Undertime Debug - Day: Thu, Date: 2025-07-03, Has Attendance: Yes, Undertime Minutes: 0, Has Undertime: No  
[2025-07-12 11:37:22] local.INFO: Undertime Debug - Day: Fri, Date: 2025-07-04, Has Attendance: Yes, Undertime Minutes: 0, Has Undertime: No  
[2025-07-12 11:37:22] local.INFO: Undertime Debug - Day: Sat, Date: 2025-07-05, Has Attendance: Yes, Undertime Minutes: 0, Has Undertime: No  
[2025-07-12 11:37:27] local.INFO: === REQUEST PARAMETERS DEBUG === {"all_request_params":{"weekly_start_date":"2025-07-06","weekly_end_date":"2025-07-12","month":"6","year":"2025"},"weekly_start_date_param":"2025-07-06","weekly_end_date_param":"2025-07-12","year_param":"2025","month_param":"6","calculated_weekly_start":"2025-07-06","calculated_weekly_end":"2025-07-12"} 
[2025-07-12 11:37:27] local.INFO: Working Days Debug {"workingDays":["Sun","Mon","Tue","Wed","Thu","Fri","Sat"],"weeklyQueryStart":"2025-07-06","weeklyQueryEnd":"2025-07-12"} 
[2025-07-12 11:37:27] local.INFO: Undertime Debug - Day: Sun, Date: 2025-07-06, Has Attendance: Yes, Undertime Minutes: 0, Has Undertime: No  
[2025-07-12 11:37:27] local.INFO: Undertime Debug - Day: Mon, Date: 2025-07-07, Has Attendance: Yes, Undertime Minutes: 0, Has Undertime: No  
[2025-07-12 11:37:27] local.INFO: Undertime Debug - Day: Tue, Date: 2025-07-08, Has Attendance: Yes, Undertime Minutes: 60, Has Undertime: Yes  
[2025-07-12 11:37:27] local.INFO: Undertime Debug - Day: Wed, Date: 2025-07-09, Has Attendance: Yes, Undertime Minutes: 83, Has Undertime: Yes  
[2025-07-12 11:37:27] local.INFO: Undertime Debug - Day: Thu, Date: 2025-07-10, Has Attendance: Yes, Undertime Minutes: 36, Has Undertime: Yes  
[2025-07-12 11:37:27] local.INFO: Undertime Debug - Day: Fri, Date: 2025-07-11, Has Attendance: Yes, Undertime Minutes: 0, Has Undertime: No  
[2025-07-12 11:37:27] local.INFO: Undertime Debug - Day: Sat, Date: 2025-07-12, Has Attendance: Yes, Undertime Minutes: 0, Has Undertime: No  
[2025-07-12 11:37:30] local.INFO: === REQUEST PARAMETERS DEBUG === {"all_request_params":{"month":"7","year":"2025","weekly_start_date":"2025-07-06","weekly_end_date":"2025-07-12"},"weekly_start_date_param":"2025-07-06","weekly_end_date_param":"2025-07-12","year_param":"2025","month_param":"7","calculated_weekly_start":"2025-07-06","calculated_weekly_end":"2025-07-12"} 
[2025-07-12 11:37:30] local.INFO: Working Days Debug {"workingDays":["Sun","Mon","Tue","Wed","Thu","Fri","Sat"],"weeklyQueryStart":"2025-07-06","weeklyQueryEnd":"2025-07-12"} 
[2025-07-12 11:37:30] local.INFO: Undertime Debug - Day: Sun, Date: 2025-07-06, Has Attendance: Yes, Undertime Minutes: 0, Has Undertime: No  
[2025-07-12 11:37:30] local.INFO: Undertime Debug - Day: Mon, Date: 2025-07-07, Has Attendance: Yes, Undertime Minutes: 0, Has Undertime: No  
[2025-07-12 11:37:30] local.INFO: Undertime Debug - Day: Tue, Date: 2025-07-08, Has Attendance: Yes, Undertime Minutes: 60, Has Undertime: Yes  
[2025-07-12 11:37:30] local.INFO: Undertime Debug - Day: Wed, Date: 2025-07-09, Has Attendance: Yes, Undertime Minutes: 83, Has Undertime: Yes  
[2025-07-12 11:37:30] local.INFO: Undertime Debug - Day: Thu, Date: 2025-07-10, Has Attendance: Yes, Undertime Minutes: 36, Has Undertime: Yes  
[2025-07-12 11:37:30] local.INFO: Undertime Debug - Day: Fri, Date: 2025-07-11, Has Attendance: Yes, Undertime Minutes: 0, Has Undertime: No  
[2025-07-12 11:37:30] local.INFO: Undertime Debug - Day: Sat, Date: 2025-07-12, Has Attendance: Yes, Undertime Minutes: 0, Has Undertime: No  
[2025-07-12 11:37:31] local.INFO: === REQUEST PARAMETERS DEBUG === {"all_request_params":{"month":"6","year":"2025","weekly_start_date":"2025-07-06","weekly_end_date":"2025-07-12"},"weekly_start_date_param":"2025-07-06","weekly_end_date_param":"2025-07-12","year_param":"2025","month_param":"6","calculated_weekly_start":"2025-07-06","calculated_weekly_end":"2025-07-12"} 
[2025-07-12 11:37:31] local.INFO: Working Days Debug {"workingDays":["Sun","Mon","Tue","Wed","Thu","Fri","Sat"],"weeklyQueryStart":"2025-07-06","weeklyQueryEnd":"2025-07-12"} 
[2025-07-12 11:37:31] local.INFO: Undertime Debug - Day: Sun, Date: 2025-07-06, Has Attendance: Yes, Undertime Minutes: 0, Has Undertime: No  
[2025-07-12 11:37:31] local.INFO: Undertime Debug - Day: Mon, Date: 2025-07-07, Has Attendance: Yes, Undertime Minutes: 0, Has Undertime: No  
[2025-07-12 11:37:31] local.INFO: Undertime Debug - Day: Tue, Date: 2025-07-08, Has Attendance: Yes, Undertime Minutes: 60, Has Undertime: Yes  
[2025-07-12 11:37:31] local.INFO: Undertime Debug - Day: Wed, Date: 2025-07-09, Has Attendance: Yes, Undertime Minutes: 83, Has Undertime: Yes  
[2025-07-12 11:37:31] local.INFO: Undertime Debug - Day: Thu, Date: 2025-07-10, Has Attendance: Yes, Undertime Minutes: 36, Has Undertime: Yes  
[2025-07-12 11:37:31] local.INFO: Undertime Debug - Day: Fri, Date: 2025-07-11, Has Attendance: Yes, Undertime Minutes: 0, Has Undertime: No  
[2025-07-12 11:37:31] local.INFO: Undertime Debug - Day: Sat, Date: 2025-07-12, Has Attendance: Yes, Undertime Minutes: 0, Has Undertime: No  
[2025-07-12 11:38:01] local.INFO: === REQUEST PARAMETERS DEBUG === {"all_request_params":{"weekly_start_date":"2025-06-29","weekly_end_date":"2025-07-05","month":"6","year":"2025"},"weekly_start_date_param":"2025-06-29","weekly_end_date_param":"2025-07-05","year_param":"2025","month_param":"6","calculated_weekly_start":"2025-06-29","calculated_weekly_end":"2025-07-05"} 
[2025-07-12 11:38:01] local.INFO: Working Days Debug {"workingDays":["Sun","Mon","Tue","Wed","Thu","Fri","Sat"],"weeklyQueryStart":"2025-06-29","weeklyQueryEnd":"2025-07-05"} 
[2025-07-12 11:38:01] local.INFO: Undertime Debug - Day: Sun, Date: 2025-06-29, Has Attendance: Yes, Undertime Minutes: 22, Has Undertime: Yes  
[2025-07-12 11:38:01] local.INFO: Undertime Debug - Day: Mon, Date: 2025-06-30, Has Attendance: Yes, Undertime Minutes: 0, Has Undertime: No  
[2025-07-12 11:38:01] local.INFO: Undertime Debug - Day: Tue, Date: 2025-07-01, Has Attendance: Yes, Undertime Minutes: 0, Has Undertime: No  
[2025-07-12 11:38:01] local.INFO: Undertime Debug - Day: Wed, Date: 2025-07-02, Has Attendance: Yes, Undertime Minutes: 0, Has Undertime: No  
[2025-07-12 11:38:01] local.INFO: Undertime Debug - Day: Thu, Date: 2025-07-03, Has Attendance: Yes, Undertime Minutes: 0, Has Undertime: No  
[2025-07-12 11:38:01] local.INFO: Undertime Debug - Day: Fri, Date: 2025-07-04, Has Attendance: Yes, Undertime Minutes: 0, Has Undertime: No  
[2025-07-12 11:38:01] local.INFO: Undertime Debug - Day: Sat, Date: 2025-07-05, Has Attendance: Yes, Undertime Minutes: 0, Has Undertime: No  
[2025-07-12 11:43:30] local.INFO: === REQUEST PARAMETERS DEBUG === {"all_request_params":{"month":"6","weekly_end_date":"2025-07-05","weekly_start_date":"2025-06-29","year":"2025"},"weekly_start_date_param":"2025-06-29","weekly_end_date_param":"2025-07-05","year_param":"2025","month_param":"6","calculated_weekly_start":"2025-06-29","calculated_weekly_end":"2025-07-05"} 
[2025-07-12 11:43:30] local.INFO: Working Days Debug {"workingDays":["Sun","Mon","Tue","Wed","Thu","Fri","Sat"],"weeklyQueryStart":"2025-06-29","weeklyQueryEnd":"2025-07-05"} 
[2025-07-12 11:43:30] local.INFO: Undertime Debug - Day: Sun, Date: 2025-06-29, Has Attendance: No, Undertime Minutes: N/A, Has Undertime: No  
[2025-07-12 11:43:30] local.INFO: Undertime Debug - Day: Mon, Date: 2025-06-30, Has Attendance: Yes, Undertime Minutes: 0, Has Undertime: No  
[2025-07-12 11:43:30] local.INFO: Undertime Debug - Day: Tue, Date: 2025-07-01, Has Attendance: Yes, Undertime Minutes: 0, Has Undertime: No  
[2025-07-12 11:43:30] local.INFO: Undertime Debug - Day: Wed, Date: 2025-07-02, Has Attendance: Yes, Undertime Minutes: 0, Has Undertime: No  
[2025-07-12 11:43:30] local.INFO: Undertime Debug - Day: Thu, Date: 2025-07-03, Has Attendance: Yes, Undertime Minutes: 0, Has Undertime: No  
[2025-07-12 11:43:30] local.INFO: Undertime Debug - Day: Fri, Date: 2025-07-04, Has Attendance: Yes, Undertime Minutes: 0, Has Undertime: No  
[2025-07-12 11:43:30] local.INFO: Undertime Debug - Day: Sat, Date: 2025-07-05, Has Attendance: Yes, Undertime Minutes: 0, Has Undertime: No  
[2025-07-12 11:43:51] local.INFO: === REQUEST PARAMETERS DEBUG === {"all_request_params":{"weekly_start_date":"2025-07-06","weekly_end_date":"2025-07-12","month":"6","year":"2025"},"weekly_start_date_param":"2025-07-06","weekly_end_date_param":"2025-07-12","year_param":"2025","month_param":"6","calculated_weekly_start":"2025-07-06","calculated_weekly_end":"2025-07-12"} 
[2025-07-12 11:43:51] local.INFO: Working Days Debug {"workingDays":["Sun","Mon","Tue","Wed","Thu","Fri","Sat"],"weeklyQueryStart":"2025-07-06","weeklyQueryEnd":"2025-07-12"} 
[2025-07-12 11:43:51] local.INFO: Undertime Debug - Day: Sun, Date: 2025-07-06, Has Attendance: Yes, Undertime Minutes: 0, Has Undertime: No  
[2025-07-12 11:43:51] local.INFO: Undertime Debug - Day: Mon, Date: 2025-07-07, Has Attendance: Yes, Undertime Minutes: 0, Has Undertime: No  
[2025-07-12 11:43:51] local.INFO: Undertime Debug - Day: Tue, Date: 2025-07-08, Has Attendance: Yes, Undertime Minutes: 60, Has Undertime: Yes  
[2025-07-12 11:43:51] local.INFO: Undertime Debug - Day: Wed, Date: 2025-07-09, Has Attendance: Yes, Undertime Minutes: 83, Has Undertime: Yes  
[2025-07-12 11:43:51] local.INFO: Undertime Debug - Day: Thu, Date: 2025-07-10, Has Attendance: Yes, Undertime Minutes: 36, Has Undertime: Yes  
[2025-07-12 11:43:51] local.INFO: Undertime Debug - Day: Fri, Date: 2025-07-11, Has Attendance: Yes, Undertime Minutes: 0, Has Undertime: No  
[2025-07-12 11:43:51] local.INFO: Undertime Debug - Day: Sat, Date: 2025-07-12, Has Attendance: Yes, Undertime Minutes: 12, Has Undertime: Yes  
[2025-07-12 11:43:55] local.INFO: === REQUEST PARAMETERS DEBUG === {"all_request_params":{"month":"6","weekly_end_date":"2025-07-12","weekly_start_date":"2025-07-06","year":"2025"},"weekly_start_date_param":"2025-07-06","weekly_end_date_param":"2025-07-12","year_param":"2025","month_param":"6","calculated_weekly_start":"2025-07-06","calculated_weekly_end":"2025-07-12"} 
[2025-07-12 11:43:55] local.INFO: Working Days Debug {"workingDays":["Sun","Mon","Tue","Wed","Thu","Fri","Sat"],"weeklyQueryStart":"2025-07-06","weeklyQueryEnd":"2025-07-12"} 
[2025-07-12 11:43:55] local.INFO: Undertime Debug - Day: Sun, Date: 2025-07-06, Has Attendance: Yes, Undertime Minutes: 0, Has Undertime: No  
[2025-07-12 11:43:55] local.INFO: Undertime Debug - Day: Mon, Date: 2025-07-07, Has Attendance: Yes, Undertime Minutes: 0, Has Undertime: No  
[2025-07-12 11:43:55] local.INFO: Undertime Debug - Day: Tue, Date: 2025-07-08, Has Attendance: Yes, Undertime Minutes: 60, Has Undertime: Yes  
[2025-07-12 11:43:55] local.INFO: Undertime Debug - Day: Wed, Date: 2025-07-09, Has Attendance: Yes, Undertime Minutes: 83, Has Undertime: Yes  
[2025-07-12 11:43:55] local.INFO: Undertime Debug - Day: Thu, Date: 2025-07-10, Has Attendance: Yes, Undertime Minutes: 36, Has Undertime: Yes  
[2025-07-12 11:43:55] local.INFO: Undertime Debug - Day: Fri, Date: 2025-07-11, Has Attendance: Yes, Undertime Minutes: 0, Has Undertime: No  
[2025-07-12 11:43:55] local.INFO: Undertime Debug - Day: Sat, Date: 2025-07-12, Has Attendance: Yes, Undertime Minutes: 12, Has Undertime: Yes  
[2025-07-12 11:44:22] local.INFO: === REQUEST PARAMETERS DEBUG === {"all_request_params":{"weekly_start_date":"2025-06-29","weekly_end_date":"2025-07-05","month":"6","year":"2025"},"weekly_start_date_param":"2025-06-29","weekly_end_date_param":"2025-07-05","year_param":"2025","month_param":"6","calculated_weekly_start":"2025-06-29","calculated_weekly_end":"2025-07-05"} 
[2025-07-12 11:44:22] local.INFO: Working Days Debug {"workingDays":["Sun","Mon","Tue","Wed","Thu","Fri","Sat"],"weeklyQueryStart":"2025-06-29","weeklyQueryEnd":"2025-07-05"} 
[2025-07-12 11:44:22] local.INFO: Undertime Debug - Day: Sun, Date: 2025-06-29, Has Attendance: No, Undertime Minutes: N/A, Has Undertime: No  
[2025-07-12 11:44:22] local.INFO: Undertime Debug - Day: Mon, Date: 2025-06-30, Has Attendance: Yes, Undertime Minutes: 0, Has Undertime: No  
[2025-07-12 11:44:22] local.INFO: Undertime Debug - Day: Tue, Date: 2025-07-01, Has Attendance: Yes, Undertime Minutes: 0, Has Undertime: No  
[2025-07-12 11:44:22] local.INFO: Undertime Debug - Day: Wed, Date: 2025-07-02, Has Attendance: Yes, Undertime Minutes: 0, Has Undertime: No  
[2025-07-12 11:44:22] local.INFO: Undertime Debug - Day: Thu, Date: 2025-07-03, Has Attendance: Yes, Undertime Minutes: 0, Has Undertime: No  
[2025-07-12 11:44:22] local.INFO: Undertime Debug - Day: Fri, Date: 2025-07-04, Has Attendance: Yes, Undertime Minutes: 0, Has Undertime: No  
[2025-07-12 11:44:22] local.INFO: Undertime Debug - Day: Sat, Date: 2025-07-05, Has Attendance: Yes, Undertime Minutes: 0, Has Undertime: No  
[2025-07-12 11:44:30] local.INFO: === REQUEST PARAMETERS DEBUG === {"all_request_params":{"month":"5","year":"2025","weekly_start_date":"2025-06-29","weekly_end_date":"2025-07-05"},"weekly_start_date_param":"2025-06-29","weekly_end_date_param":"2025-07-05","year_param":"2025","month_param":"5","calculated_weekly_start":"2025-06-29","calculated_weekly_end":"2025-07-05"} 
[2025-07-12 11:44:30] local.INFO: Working Days Debug {"workingDays":["Sun","Mon","Tue","Wed","Thu","Fri","Sat"],"weeklyQueryStart":"2025-06-29","weeklyQueryEnd":"2025-07-05"} 
[2025-07-12 11:44:30] local.INFO: Undertime Debug - Day: Sun, Date: 2025-06-29, Has Attendance: No, Undertime Minutes: N/A, Has Undertime: No  
[2025-07-12 11:44:30] local.INFO: Undertime Debug - Day: Mon, Date: 2025-06-30, Has Attendance: Yes, Undertime Minutes: 0, Has Undertime: No  
[2025-07-12 11:44:30] local.INFO: Undertime Debug - Day: Tue, Date: 2025-07-01, Has Attendance: Yes, Undertime Minutes: 0, Has Undertime: No  
[2025-07-12 11:44:30] local.INFO: Undertime Debug - Day: Wed, Date: 2025-07-02, Has Attendance: Yes, Undertime Minutes: 0, Has Undertime: No  
[2025-07-12 11:44:30] local.INFO: Undertime Debug - Day: Thu, Date: 2025-07-03, Has Attendance: Yes, Undertime Minutes: 0, Has Undertime: No  
[2025-07-12 11:44:30] local.INFO: Undertime Debug - Day: Fri, Date: 2025-07-04, Has Attendance: Yes, Undertime Minutes: 0, Has Undertime: No  
[2025-07-12 11:44:30] local.INFO: Undertime Debug - Day: Sat, Date: 2025-07-05, Has Attendance: Yes, Undertime Minutes: 0, Has Undertime: No  
[2025-07-12 11:44:31] local.INFO: === REQUEST PARAMETERS DEBUG === {"all_request_params":{"month":"6","year":"2025","weekly_start_date":"2025-06-29","weekly_end_date":"2025-07-05"},"weekly_start_date_param":"2025-06-29","weekly_end_date_param":"2025-07-05","year_param":"2025","month_param":"6","calculated_weekly_start":"2025-06-29","calculated_weekly_end":"2025-07-05"} 
[2025-07-12 11:44:31] local.INFO: Working Days Debug {"workingDays":["Sun","Mon","Tue","Wed","Thu","Fri","Sat"],"weeklyQueryStart":"2025-06-29","weeklyQueryEnd":"2025-07-05"} 
[2025-07-12 11:44:31] local.INFO: Undertime Debug - Day: Sun, Date: 2025-06-29, Has Attendance: No, Undertime Minutes: N/A, Has Undertime: No  
[2025-07-12 11:44:31] local.INFO: Undertime Debug - Day: Mon, Date: 2025-06-30, Has Attendance: Yes, Undertime Minutes: 0, Has Undertime: No  
[2025-07-12 11:44:31] local.INFO: Undertime Debug - Day: Tue, Date: 2025-07-01, Has Attendance: Yes, Undertime Minutes: 0, Has Undertime: No  
[2025-07-12 11:44:31] local.INFO: Undertime Debug - Day: Wed, Date: 2025-07-02, Has Attendance: Yes, Undertime Minutes: 0, Has Undertime: No  
[2025-07-12 11:44:31] local.INFO: Undertime Debug - Day: Thu, Date: 2025-07-03, Has Attendance: Yes, Undertime Minutes: 0, Has Undertime: No  
[2025-07-12 11:44:31] local.INFO: Undertime Debug - Day: Fri, Date: 2025-07-04, Has Attendance: Yes, Undertime Minutes: 0, Has Undertime: No  
[2025-07-12 11:44:31] local.INFO: Undertime Debug - Day: Sat, Date: 2025-07-05, Has Attendance: Yes, Undertime Minutes: 0, Has Undertime: No  
[2025-07-12 11:44:33] local.INFO: === REQUEST PARAMETERS DEBUG === {"all_request_params":{"month":"7","year":"2025","weekly_start_date":"2025-06-29","weekly_end_date":"2025-07-05"},"weekly_start_date_param":"2025-06-29","weekly_end_date_param":"2025-07-05","year_param":"2025","month_param":"7","calculated_weekly_start":"2025-06-29","calculated_weekly_end":"2025-07-05"} 
[2025-07-12 11:44:33] local.INFO: Working Days Debug {"workingDays":["Sun","Mon","Tue","Wed","Thu","Fri","Sat"],"weeklyQueryStart":"2025-06-29","weeklyQueryEnd":"2025-07-05"} 
[2025-07-12 11:44:33] local.INFO: Undertime Debug - Day: Sun, Date: 2025-06-29, Has Attendance: No, Undertime Minutes: N/A, Has Undertime: No  
[2025-07-12 11:44:33] local.INFO: Undertime Debug - Day: Mon, Date: 2025-06-30, Has Attendance: Yes, Undertime Minutes: 0, Has Undertime: No  
[2025-07-12 11:44:33] local.INFO: Undertime Debug - Day: Tue, Date: 2025-07-01, Has Attendance: Yes, Undertime Minutes: 0, Has Undertime: No  
[2025-07-12 11:44:33] local.INFO: Undertime Debug - Day: Wed, Date: 2025-07-02, Has Attendance: Yes, Undertime Minutes: 0, Has Undertime: No  
[2025-07-12 11:44:33] local.INFO: Undertime Debug - Day: Thu, Date: 2025-07-03, Has Attendance: Yes, Undertime Minutes: 0, Has Undertime: No  
[2025-07-12 11:44:33] local.INFO: Undertime Debug - Day: Fri, Date: 2025-07-04, Has Attendance: Yes, Undertime Minutes: 0, Has Undertime: No  
[2025-07-12 11:44:33] local.INFO: Undertime Debug - Day: Sat, Date: 2025-07-05, Has Attendance: Yes, Undertime Minutes: 0, Has Undertime: No  
[2025-07-12 11:44:39] local.INFO: === REQUEST PARAMETERS DEBUG === {"all_request_params":{"weekly_start_date":"2025-06-01","weekly_end_date":"2025-06-07","month":"7","year":"2025"},"weekly_start_date_param":"2025-06-01","weekly_end_date_param":"2025-06-07","year_param":"2025","month_param":"7","calculated_weekly_start":"2025-06-01","calculated_weekly_end":"2025-06-07"} 
[2025-07-12 11:44:39] local.INFO: Working Days Debug {"workingDays":["Sun","Mon","Tue","Wed","Thu","Fri","Sat"],"weeklyQueryStart":"2025-06-01","weeklyQueryEnd":"2025-06-07"} 
[2025-07-12 11:44:39] local.INFO: Undertime Debug - Day: Sun, Date: 2025-06-01, Has Attendance: Yes, Undertime Minutes: 0, Has Undertime: No  
[2025-07-12 11:44:39] local.INFO: Undertime Debug - Day: Mon, Date: 2025-06-02, Has Attendance: Yes, Undertime Minutes: 0, Has Undertime: No  
[2025-07-12 11:44:39] local.INFO: Undertime Debug - Day: Tue, Date: 2025-06-03, Has Attendance: Yes, Undertime Minutes: 200, Has Undertime: Yes  
[2025-07-12 11:44:39] local.INFO: Undertime Debug - Day: Wed, Date: 2025-06-04, Has Attendance: Yes, Undertime Minutes: 0, Has Undertime: No  
[2025-07-12 11:44:39] local.INFO: Undertime Debug - Day: Thu, Date: 2025-06-05, Has Attendance: Yes, Undertime Minutes: 0, Has Undertime: No  
[2025-07-12 11:44:39] local.INFO: Undertime Debug - Day: Fri, Date: 2025-06-06, Has Attendance: Yes, Undertime Minutes: 0, Has Undertime: No  
[2025-07-12 11:44:39] local.INFO: Undertime Debug - Day: Sat, Date: 2025-06-07, Has Attendance: Yes, Undertime Minutes: 0, Has Undertime: No  
[2025-07-12 11:45:06] local.INFO: === REQUEST PARAMETERS DEBUG === {"all_request_params":{"weekly_start_date":"2025-06-29","weekly_end_date":"2025-07-05","month":"7","year":"2025"},"weekly_start_date_param":"2025-06-29","weekly_end_date_param":"2025-07-05","year_param":"2025","month_param":"7","calculated_weekly_start":"2025-06-29","calculated_weekly_end":"2025-07-05"} 
[2025-07-12 11:45:06] local.INFO: Working Days Debug {"workingDays":["Sun","Mon","Tue","Wed","Thu","Fri","Sat"],"weeklyQueryStart":"2025-06-29","weeklyQueryEnd":"2025-07-05"} 
[2025-07-12 11:45:06] local.INFO: Undertime Debug - Day: Sun, Date: 2025-06-29, Has Attendance: No, Undertime Minutes: N/A, Has Undertime: No  
[2025-07-12 11:45:06] local.INFO: Undertime Debug - Day: Mon, Date: 2025-06-30, Has Attendance: Yes, Undertime Minutes: 0, Has Undertime: No  
[2025-07-12 11:45:06] local.INFO: Undertime Debug - Day: Tue, Date: 2025-07-01, Has Attendance: Yes, Undertime Minutes: 0, Has Undertime: No  
[2025-07-12 11:45:06] local.INFO: Undertime Debug - Day: Wed, Date: 2025-07-02, Has Attendance: Yes, Undertime Minutes: 0, Has Undertime: No  
[2025-07-12 11:45:06] local.INFO: Undertime Debug - Day: Thu, Date: 2025-07-03, Has Attendance: Yes, Undertime Minutes: 0, Has Undertime: No  
[2025-07-12 11:45:06] local.INFO: Undertime Debug - Day: Fri, Date: 2025-07-04, Has Attendance: Yes, Undertime Minutes: 0, Has Undertime: No  
[2025-07-12 11:45:06] local.INFO: Undertime Debug - Day: Sat, Date: 2025-07-05, Has Attendance: Yes, Undertime Minutes: 0, Has Undertime: No  
[2025-07-12 11:45:13] local.INFO: === REQUEST PARAMETERS DEBUG === {"all_request_params":{"weekly_start_date":"2025-07-06","weekly_end_date":"2025-07-12","month":"7","year":"2025"},"weekly_start_date_param":"2025-07-06","weekly_end_date_param":"2025-07-12","year_param":"2025","month_param":"7","calculated_weekly_start":"2025-07-06","calculated_weekly_end":"2025-07-12"} 
[2025-07-12 11:45:13] local.INFO: Working Days Debug {"workingDays":["Sun","Mon","Tue","Wed","Thu","Fri","Sat"],"weeklyQueryStart":"2025-07-06","weeklyQueryEnd":"2025-07-12"} 
[2025-07-12 11:45:13] local.INFO: Undertime Debug - Day: Sun, Date: 2025-07-06, Has Attendance: Yes, Undertime Minutes: 0, Has Undertime: No  
[2025-07-12 11:45:13] local.INFO: Undertime Debug - Day: Mon, Date: 2025-07-07, Has Attendance: Yes, Undertime Minutes: 0, Has Undertime: No  
[2025-07-12 11:45:13] local.INFO: Undertime Debug - Day: Tue, Date: 2025-07-08, Has Attendance: Yes, Undertime Minutes: 60, Has Undertime: Yes  
[2025-07-12 11:45:13] local.INFO: Undertime Debug - Day: Wed, Date: 2025-07-09, Has Attendance: Yes, Undertime Minutes: 83, Has Undertime: Yes  
[2025-07-12 11:45:13] local.INFO: Undertime Debug - Day: Thu, Date: 2025-07-10, Has Attendance: Yes, Undertime Minutes: 36, Has Undertime: Yes  
[2025-07-12 11:45:13] local.INFO: Undertime Debug - Day: Fri, Date: 2025-07-11, Has Attendance: Yes, Undertime Minutes: 0, Has Undertime: No  
[2025-07-12 11:45:13] local.INFO: Undertime Debug - Day: Sat, Date: 2025-07-12, Has Attendance: Yes, Undertime Minutes: 12, Has Undertime: Yes  
